<?php

/**
 * 进程内模型实例数据同步功能使用示例
 * @description: 演示如何使用模型实例数据同步功能
 * @author: AI
 * @date: 2024-11-04
 */

require_once __DIR__ . '/../app/config/loader.php';

use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;

class ModelSyncExample
{
    /**
     * 基础同步示例
     */
    public static function basicSyncExample()
    {
        echo "=== 基础同步示例 ===" . PHP_EOL;
        
        // 确保同步功能启用
        BackyardBaseModel::enableSync();
        
        // 创建两个相同记录的实例
        $config1 = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");
        $config2 = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");
        
        if (!$config1 || !$config2) {
            echo "示例记录不存在" . PHP_EOL;
            return;
        }
        
        echo "初始值:" . PHP_EOL;
        echo "Config1: " . ($config1->set_val ?? 'NULL') . PHP_EOL;
        echo "Config2: " . ($config2->set_val ?? 'NULL') . PHP_EOL;
        
        // 修改第一个实例
        $newValue = 'sync_demo_' . date('Y-m-d H:i:s');
        $config1->set_val = $newValue;
        $config1->save();
        
        echo PHP_EOL . "保存后:" . PHP_EOL;
        echo "Config1: " . $config1->set_val . PHP_EOL;
        echo "Config2: " . $config2->set_val . " (自动同步)" . PHP_EOL;
        
        // 验证同步结果
        if ($config1->set_val === $config2->set_val) {
            echo "✅ 同步成功！" . PHP_EOL;
        } else {
            echo "❌ 同步失败！" . PHP_EOL;
        }
    }
    
    /**
     * 配置管理示例
     */
    public static function configManagementExample()
    {
        echo PHP_EOL . "=== 配置管理示例 ===" . PHP_EOL;
        
        // 模拟多个组件同时使用同一配置
        $systemConfig = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");
        $userConfig = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");
        $apiConfig = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");
        
        if (!$systemConfig) {
            echo "配置记录不存在" . PHP_EOL;
            return;
        }
        
        echo "系统组件读取配置: " . ($systemConfig->set_val ?? 'NULL') . PHP_EOL;
        echo "用户组件读取配置: " . ($userConfig->set_val ?? 'NULL') . PHP_EOL;
        echo "API组件读取配置: " . ($apiConfig->set_val ?? 'NULL') . PHP_EOL;
        
        // 管理员更新配置
        echo PHP_EOL . "管理员更新配置..." . PHP_EOL;
        $systemConfig->set_val = 'updated_by_admin_' . time();
        $systemConfig->save();
        
        // 所有组件立即获得最新配置
        echo "系统组件配置: " . $systemConfig->set_val . PHP_EOL;
        echo "用户组件配置: " . $userConfig->set_val . " (自动同步)" . PHP_EOL;
        echo "API组件配置: " . $apiConfig->set_val . " (自动同步)" . PHP_EOL;
        
        echo "✅ 所有组件配置已同步更新！" . PHP_EOL;
    }
    
    /**
     * 性能监控示例
     */
    public static function performanceMonitoringExample()
    {
        echo PHP_EOL . "=== 性能监控示例 ===" . PHP_EOL;
        
        // 创建多个实例
        $instances = [];
        for ($i = 0; $i < 5; $i++) {
            $instances[] = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");
        }
        
        // 显示实例统计
        $stats = BackyardBaseModel::getInstanceStats();
        echo "实例统计信息:" . PHP_EOL;
        echo "- 总实例数: " . $stats['total_instances'] . PHP_EOL;
        echo "- 按类统计: " . json_encode($stats['by_class'], JSON_UNESCAPED_UNICODE) . PHP_EOL;
        echo "- 按主键统计: " . json_encode($stats['by_primary_key'], JSON_UNESCAPED_UNICODE) . PHP_EOL;
        
        // 测试同步性能
        $startTime = microtime(true);
        $instances[0]->set_val = 'perf_monitor_' . time();
        $instances[0]->save();
        $endTime = microtime(true);
        
        $syncTime = ($endTime - $startTime) * 1000;
        echo "同步耗时: " . number_format($syncTime, 2) . " ms" . PHP_EOL;
        
        // 验证同步结果
        $syncedCount = 0;
        foreach ($instances as $instance) {
            if ($instance->set_val === $instances[0]->set_val) {
                $syncedCount++;
            }
        }
        echo "同步成功率: " . ($syncedCount / count($instances) * 100) . "%" . PHP_EOL;
    }
    
    /**
     * 控制功能示例
     */
    public static function controlFeaturesExample()
    {
        echo PHP_EOL . "=== 控制功能示例 ===" . PHP_EOL;
        
        // 检查当前状态
        echo "当前同步状态: " . (BackyardBaseModel::isSyncEnabled() ? '启用' : '禁用') . PHP_EOL;
        
        // 禁用同步
        BackyardBaseModel::disableSync();
        echo "已禁用同步功能" . PHP_EOL;
        
        // 创建实例并测试
        $model1 = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");
        $model2 = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");
        
        if ($model1 && $model2) {
            $originalValue = $model2->set_val;
            $model1->set_val = 'no_sync_' . time();
            $model1->save();
            
            echo "禁用同步后:" . PHP_EOL;
            echo "Model1: " . $model1->set_val . PHP_EOL;
            echo "Model2: " . $model2->set_val . " (应该保持原值)" . PHP_EOL;
            
            if ($model2->set_val === $originalValue) {
                echo "✅ 同步已正确禁用" . PHP_EOL;
            } else {
                echo "❌ 同步禁用失败" . PHP_EOL;
            }
        }
        
        // 重新启用同步
        BackyardBaseModel::enableSync();
        echo "已重新启用同步功能" . PHP_EOL;
    }
    
    /**
     * 环境变量控制示例
     */
    public static function environmentControlExample()
    {
        echo PHP_EOL . "=== 环境变量控制示例 ===" . PHP_EOL;
        
        // 通过环境变量禁用同步
        putenv('SKIP_MODEL_AUTO_REFRESH=1');
        echo "设置环境变量: SKIP_MODEL_AUTO_REFRESH=1" . PHP_EOL;
        
        // 即使代码中启用同步，环境变量也会覆盖
        BackyardBaseModel::enableSync();
        
        $model1 = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");
        $model2 = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");
        
        if ($model1 && $model2) {
            $originalValue = $model2->set_val;
            $model1->set_val = 'env_control_' . time();
            $model1->save();
            
            echo "环境变量控制下:" . PHP_EOL;
            echo "Model1: " . $model1->set_val . PHP_EOL;
            echo "Model2: " . $model2->set_val . " (环境变量禁用同步)" . PHP_EOL;
            
            if ($model2->set_val === $originalValue) {
                echo "✅ 环境变量控制生效" . PHP_EOL;
            } else {
                echo "❌ 环境变量控制失效" . PHP_EOL;
            }
        }
        
        // 清除环境变量
        putenv('SKIP_MODEL_AUTO_REFRESH');
        echo "已清除环境变量" . PHP_EOL;
    }
    
    /**
     * 内存管理示例
     */
    public static function memoryManagementExample()
    {
        echo PHP_EOL . "=== 内存管理示例 ===" . PHP_EOL;
        
        // 创建大量实例
        echo "创建大量实例..." . PHP_EOL;
        $instances = [];
        for ($i = 0; $i < 20; $i++) {
            $instances[] = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");
        }
        
        $stats = BackyardBaseModel::getInstanceStats();
        echo "创建后实例数: " . $stats['total_instances'] . PHP_EOL;
        
        // 清理实例引用
        unset($instances);
        
        // 手动触发垃圾回收
        gc_collect_cycles();
        
        $stats = BackyardBaseModel::getInstanceStats();
        echo "垃圾回收后实例数: " . $stats['total_instances'] . PHP_EOL;
        
        // 手动清理所有实例
        BackyardBaseModel::clearAllInstances();
        $stats = BackyardBaseModel::getInstanceStats();
        echo "手动清理后实例数: " . $stats['total_instances'] . PHP_EOL;
        
        echo "✅ 内存管理完成" . PHP_EOL;
    }
    
    /**
     * 运行所有示例
     */
    public static function runAllExamples()
    {
        echo "开始运行所有示例..." . PHP_EOL . PHP_EOL;
        
        self::basicSyncExample();
        self::configManagementExample();
        self::performanceMonitoringExample();
        self::controlFeaturesExample();
        self::environmentControlExample();
        self::memoryManagementExample();
        
        echo PHP_EOL . "所有示例运行完成！" . PHP_EOL;
    }
}

// 如果直接运行此文件，执行所有示例
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    ModelSyncExample::runAllExamples();
}
