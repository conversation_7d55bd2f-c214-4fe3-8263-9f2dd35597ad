<?php
namespace FlashExpress\bi\App\Controllers;

use App\Country\Tools;
use  FlashExpress\backyard\App\Controllers;
use  FlashExpress\bi\App\Controllers\ToolController;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\HrStaffContractEnums;
use FlashExpress\bi\App\Enums\PushEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;
use FlashExpress\bi\App\Models\backyard\SalaryApproveModel;
use FlashExpress\bi\App\Modules\Ph\Server\ProbationServer;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\OtherRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Repository\ReportRepository;
use FlashExpress\bi\App\Repository\ResignRepository;
use FlashExpress\bi\App\Repository\WmsRepository;
use FlashExpress\bi\App\Server\AssetServer;
use FlashExpress\bi\App\Server\AssetWorkOrderServer;
use FlashExpress\bi\App\Server\AttendanceCalendarServer;
use FlashExpress\bi\App\Server\AttendanceServer;
use FlashExpress\bi\App\Server\CancelContractServer;
use FlashExpress\bi\App\Server\CeoMailServer;
use FlashExpress\bi\App\Server\CompanyTerminationContractServer;
use FlashExpress\bi\App\Server\FlashPayServer;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\JobTransferModel;
use FlashExpress\bi\App\Repository\SysListRepository;
use FlashExpress\bi\App\Server\HcServer;
use FlashExpress\bi\App\Server\HireTypeChangeServer;
use FlashExpress\bi\App\Server\HrStaffRenewContractServer;
use FlashExpress\bi\App\Server\JobTransferSpecialServer;
use FlashExpress\bi\App\Server\LeaveServer;
use FlashExpress\bi\App\Server\LoginUserServer;
use FlashExpress\bi\App\Server\MaterialWmsServer;
use FlashExpress\bi\App\Server\OfferSignApproveServer;
use FlashExpress\bi\App\Server\Osm\LoginServer;
use FlashExpress\bi\App\Server\OsStaffServer;
use FlashExpress\bi\App\Server\OvertimeServer;
use FlashExpress\bi\App\Server\JobtransferServer;
use FlashExpress\bi\App\Server\Penalty\AttendancePenaltyServer;
use FlashExpress\bi\App\Server\PersoninfoServer;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\QuitclaimServer;
use FlashExpress\bi\App\Server\ReminderServer;
use FlashExpress\bi\App\Server\RenewContractBusinessServer;
use FlashExpress\bi\App\Server\ResignServer;
use FlashExpress\bi\App\Server\Rpc\ApprovalRpc;
use FlashExpress\bi\App\Server\Rpc\BusinessTrip;
use FlashExpress\bi\App\Server\Rpc\Demo;
use FlashExpress\bi\App\Server\Rpc\InternalRecruitRpc;
use FlashExpress\bi\App\Server\Rpc\LoginCheck;
use FlashExpress\bi\App\Server\Rpc\MessageRpc;
use FlashExpress\bi\App\Server\Rpc\StaffManage;
use FlashExpress\bi\App\Server\Rpc\FleetRpc;
use FlashExpress\bi\App\Server\Rpc\JobTransfer;
use FlashExpress\bi\App\Server\Rpc\Overtime;
use FlashExpress\bi\App\Server\Rpc\Probation;
use FlashExpress\bi\App\Server\Rpc\Store;
use FlashExpress\bi\App\Server\Rpc\SuspensionRpc;
use FlashExpress\bi\App\Server\Rpc\Personinfo;
use FlashExpress\bi\App\Server\Rpc\SupportRpc;
use FlashExpress\bi\App\Server\Rpc\Vacation;
use FlashExpress\bi\App\Server\RPCStub;
use FlashExpress\bi\App\Server\SalaryServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StatisticsAttendanceServer;
use FlashExpress\bi\App\Server\SysStoreServer;
use FlashExpress\bi\App\Server\TicketServer;
use FlashExpress\bi\App\Server\ToolServer;
use FlashExpress\bi\App\Server\VehicleServer;
use FlashExpress\bi\App\Server\WmsServer;
use FlashExpress\bi\App\Server\WorkbenchServer;
use FlashExpress\bi\App\Server\WorkflowManagementServer;
use JsonRPC\Server as JsonRPC_Server;
use FlashExpress\bi\App\Server\MessageServer;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\BusinesstripRepository;
use FlashExpress\bi\App\Server\AuditServer;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Server\AuditListServer;
use FlashExpress\bi\App\Server\AttendanceBusinessServer;
use FlashExpress\bi\App\Server\MessageCenterServer;
use FlashExpress\bi\App\Server\HrStaffContractServer;
use FlashExpress\bi\App\Server\FuelBudgetServer;
use FlashExpress\bi\App\Server\PenaltyAppealServer;
use FlashExpress\bi\App\Models\fle\StaffInfoModel;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\InteriorOrderServer;
use FlashExpress\bi\App\Server\SystemExternalApprovalServer;
use Exception;
use FlashExpress\bi\App\Server\WorkflowServer;

use FlashExpress\bi\App\Server\ReportServer;
/**
 * bi对外提供服务
 * Class SvcController
 */
class SvcController extends \FlashExpress\bi\App\Core\PhalBaseController
{
    public $timezone;
    public $locale;
    public function callAction()
    {
        if (function_exists('molten_get_traceid')) {
            header('traceid:' . molten_get_traceid());
        }

        $server = new JsonRPC_Server();
        $procedureHandler = $server->getProcedureHandler();

        $di = $this->getDI();
        $timezone = $di->getConfig()->application->timeZone;
        $add_hour = $this->getDI()['config']['application']['add_hour'];

        $procedureHandler->withCallback('hello', function ($locale, $a) use($timezone) {

            return $timezone;
            //echo 'hello world';
            return $a;
        })->withCallback('getStaffRecord', function ($locale, $params) use($timezone) {
            $data = [];
            if (!isset($params['mouth'], $params['staff_info_id']) || $params['mouth'] <= 0) {
                return $data;
            }
            $countryCode = ucfirst(strtolower($this->config->application->country_code));
            if($countryCode == 'Th'){
                //$vehic_os = new VehicleServer($locale['locale'], $timezone);
            }else{
                $country_server = sprintf("\FlashExpress\bi\App\Modules\%s\Server\VehicleServer",$countryCode);
                //$vehic_os = new $country_server($locale['locale'], $timezone);
            }
            $vehic_os = new VehicleServer($locale['locale'], $timezone);
            $list = $vehic_os->getStaffRecord($params['mouth'], $params['staff_info_id']);
            return $list;
        })->withCallback('getVehicleInfoByStaffID', function ($locale, $params) use($timezone) {
            $data = [];
            if (!isset($params['staff_info_id'])) {
                return $data;
            }
            $vehic_os = new VehicleServer($locale['locale'], $timezone);
            $list = $vehic_os->getVehicleInfoByStaffID($params['staff_info_id']);
            return $list;
        })->withCallback('getStaffVehicle', function ($locale, $params) use($timezone) {
            if (!isset($params['staff_info_id'])) {
                return 0;
            }
            $vehic_os = new VehicleServer($locale['locale'], $timezone);
            return $vehic_os->getStaffVehicle($params['staff_info_id']);
        })->withCallback('getStaffMileage', function ($locale, $params) use($timezone) {
            if ( !isset($params['staff_info_id']) || !isset($params['mileage_date']) ) {
                return 0;
            }
            $vehic_os = new VehicleServer($locale['locale'], $timezone);
            return $vehic_os->getStaffMileageS($params);
        })
        //更新里程
        ->withCallback('updateStaffMileage', function ($locale, $params) use($timezone) {
            if ( !isset($params['staff_info_id']) || !isset($params['mileage_date']) ) {
                return 0;
            }
            if ( $params['start_kilometres'] > $params['end_kilometres'] ) {
                return 0;
            }
            $vehic_os = new VehicleServer($locale['locale'], $timezone);
            return $vehic_os->updateStaffMileageS($params);
        })->withCallback('sendSms', function ($locale, $params) {
            if (!isset($params['staff_info_id'])) {
                return 0;
            }


            $tool = new \ToolController();
            $res = $tool->sendSms($locale,$params);
            return $res;
        })->withCallback('staffMobileAdd', function ($locale, $params) {
            if (!isset($params['staff_info_id'])) {
                return 0;
            }
            $tool = new \ToolController();
            $res = $tool->staffMobileAdd($locale,$params);
            return $res;
        })->withCallback('getStaffMobile', function ($locale, $params) {
            if (!isset($params['staff_info_id'])) {
                return 0;
            }
            $di = $this->getDI();
            $sql = "--
                        select * from staff_mobile where create_id={$params['staff_info_id']}";
            $info = $di->get('db')->fetchOne($sql);
            return $info;
        })
        //获取警告书员工是否签名
        ->withCallback('get_warning_sign', function ($locale, $params) use($timezone) {
            if (empty($params['staff_info_id']) || empty($params['msg_id'])) {
                return 0;
            }
            $message_model = new MessageServer($locale['locale'],$timezone);
            $warning_info = $message_model->get_warning($params['staff_info_id'], $params['msg_id']);
            return $warning_info;
        })


        //举报 枚举下拉框值  和是否 有添加举报权限
        ->withCallback('report_enum', function ($locale, $params) use($timezone) {
            $user_info['staff_id'] = intval($params['operator_id']);//操作人 登陆人
            $param['staff_id'] = intval($params['staff_info_id']);//被举报人
            $param['is_svc'] = true;
            if(empty($user_info['staff_id'] || $param['staff_id']))
                return array('code' => 0,'msg' => 'parameter error','data' => null);

            //获取枚举类型
            $server = Tools::reBuildCountryInstance(new ReportServer($locale['locale'],$timezone),[$locale['locale'],$timezone]);
            $data = $server->dictReportS($user_info,$param);

            //获取 入口权限
            $audit_server = new AuditServer($locale['locale'],$timezone);
            $data['data']['permission'] = $audit_server->getReportPermission($user_info);


            return array('code' => 1,'msg' => 'success','data' => $data['data']);
        })

        //添加 举报接口
        ->withCallback('add_report', function ($locale, $params) use($timezone) {
            $re = new StaffRepository($locale['locale']);
            $user_info = $re->getStaffPositionv3($params['operator_id']);
            if(empty($user_info))
                return array('code' => 0,'msg' => 'parameter operator_id error','data' => null);
            $user_info['staff_id'] = $user_info['id'];//里面没有 staff id 我为了不改里面的东西 就拼进去一个
            if(!empty($params['image_path'])){
                $img_arr = array();
                foreach ($params['image_path'] as $v){
                    $str = convertImgUrl($v['host'],$v['path']);
                    $img_arr[] = $str;
                }
                $params['image_path'] = $img_arr;
            }

            $server = new ReportServer($locale['locale'],$timezone);
            $return = $server->addReportS($params,$user_info);
            return $return;
        })
        ->withCallback('attendance', function ($locale, $param) use($timezone,$add_hour) {
            try{
                $server = $this->class_factory('AttendanceServer',$locale['locale']);
                $return = $server->toolReissueCard($param);
                return $return;
            } catch (ValidationException $e) {
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            } catch (\Exception $e){
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                self::$DI->get('logger')->write_log('svc:attendance'.$e->getMessage());
                return $return;
            }
        })
        //ot
        ->withCallback('overtime', function ($locale, $param) use($timezone) {
            try{
                $this->locale = $locale['locale'];
                $ot_server = $this->class_factory('OvertimeServer',$locale['locale']);
                $param['is_bi'] = 1;
                $data = $ot_server->addOvertimeV3($param);
                $this->getDI()->get('logger')->write_log("bi overtime " .json_encode($data), 'info');
                return $data;
            }catch (\Exception $e){
                $this->getDI()->get('logger')->write_log("bi overtime " . $e->getMessage(), 'info');
                return array('code' => -1, 'msg' => $e->getMessage(), 'data' => null);
            }
        })
        //请假
        ->withCallback('leave', function ($locale, $param) use($timezone) {
            $param['is_bi'] = 1;
            $this->locale = $locale['locale'];
            $audit_server = $this->class_factory('AuditServer',$locale['locale']);
            $newVersionType = $audit_server->newVersionType;
            //只有泰国和马来 的年假 改版 其他还没有 新增印尼
            if(!empty($newVersionType) && in_array($param['leave_type'],$newVersionType)
                || (isCountry('TH') && $param['leave_type'] == enums::LEAVE_TYPE_38)//泰国病假 特殊类型 不在 新版本里面
            ){
                $leaveServer = $this->class_factory('LeaveServer',$locale['locale']);
                return $leaveServer->saveVacation($param);
            }else{
                return $audit_server->leaveAdd($param);
            }
        })

        //请假计算天数
        ->withCallback('conversionTime', function ($locale, $param) use($timezone) {
            $param['type'] = 1;//外部调用
            $this->locale = $locale['locale'];
            $audit_server = $this->class_factory('AuditServer',$locale['locale']);
            return $audit_server->conversionTime($param);
        })

        //假期类型  对应国家 的所有的 当前实时类型
        ->withCallback('leaveTypeBook', function ($locale, $param) use($timezone) {

            $return['code'] = -1;
            $return['msg'] = '';
            try {
                $this->locale = $locale['locale'];
                $audit_server = $this->class_factory('AuditServer',$locale['locale']);
                $data = $audit_server->type_book();
                $return['code'] = 1;
                $return['data'] = $data;
            }catch (\Exception $e){
                $return['msg'] = $e->getMessage();

            }
            return $return;
        })

        //假期类型  对应各个国家 历史所有假期类型
        ->withCallback('leaveTypeBookAll', function ($locale, $param) use($timezone) {
            $return['code'] = -1;
            $return['msg'] = '';
            try {
                $this->locale = $locale['locale'];
                $audit_server = $this->class_factory('AuditServer',$locale['locale']);
                $data = $audit_server->type_book_history();
                $return['code'] = 1;
                $return['data'] = $data;
            }catch (\Exception $e){
                $return['msg'] = $e->getMessage();

            }
            return $return;
        })
        //假期类型  根据员工id  返回对应的可申请类型
        ->withCallback('staffLeaveTypeBook', function ($locale, $param) use($timezone) {
            $return['code'] = -1;
            $return['msg'] = '';
            try {
                $this->locale = $locale['locale'];
                $audit_server = $this->class_factory('AuditServer',$locale['locale']);
                //员工信息
                $staffRe = new StaffRepository($this->locale);
                $staffInfo = $staffRe->getStaffPosition($param['staff_id']);
                if(empty($staffInfo)){
                    return ['code' => ErrCode::SUCCESS,'data' => []];
                }

                $data = $audit_server->staffLeaveType($staffInfo);
                $return['code'] = 1;
                $return['data'] = array_values($data);
            }catch (\Exception $e){
                $return['msg'] = $e->getMessage();

            }
            return $return;
        })

        //请假类型 子类型
        ->withCallback('leaveTypeSubBook', function ($locale, $param) use($timezone) {
            $return['code'] = -1;
            $return['msg'] = '';
            try {
                $this->locale = $locale['locale'];
                if(empty($param['template_type']))
                    $return['msg'] = 'need template_type';
                else{
                    $audit_server = $this->class_factory('AuditServer',$locale['locale']);
                    $data = $audit_server->get_template(intval($param['template_type']));
                    $return['code'] = 1;
                    $return['data'] = $data;
                }
            }catch (\Exception $e){
                $return['msg'] = $e->getMessage();

            }
            return $return;
        })
        //获取带薪事假请假原因
        ->withCallback('getPaidLeaveReason', function ($locale, $param) use($timezone) {
            $return['code'] = -1;
            $return['msg'] = '';
            try {
                $this->locale = $locale['locale'];
                $audit_server = $this->class_factory('LeaveServer', $locale['locale']);
                $data = $audit_server->getPaidLeaveReasonList();
                $return['code'] = 1;
                $return['data'] = $data;
            }catch (\Exception $e){
                $return['msg'] = $e->getMessage();

            }
            return $return;
        })

        //当前所有有效加班类型
        ->withCallback('otTypeBook', function ($locale, $param) use($timezone) {

            $return['code'] = -1;
            $return['msg'] = '';
            try{
                $this->locale = $locale['locale'];
                $ot_server = $this->class_factory('OvertimeServer',$locale['locale']);
                $data = $ot_server->getTypeOvertime(array('is_svc' => 1));
                $res = $data['data']['dataList'];

                if(!empty($res)){
                    foreach ($res as &$v){
                        $v['msg'] .= ' ' . $v['sub_msg'];
                    }
                }
                $return['code'] = 1;
                $return['data'] = $res;
            }catch (\Exception $e){
                $return['msg'] = $e->getMessage();
            }
            return $return;
        })

        //获取 历史所有加班类型
        ->withCallback('getApprovalOtType', function ($locale, $param) use($timezone) {
            $return['code'] = -1;
            $return['msg'] = '';
            try{
                $this->locale = $locale['locale'];
                $ot_server = $this->class_factory('OvertimeServer',$locale['locale']);
                $res = $ot_server->getApprovalOtType($locale['locale']);

                if(!empty($res)){
                    foreach ($res as &$v){
                        $v['msg'] .= ' ' . $v['sub_msg'];
                    }
                }
                $return['code'] = 1;
                $return['data'] = $res;
            }catch (\Exception $e){
                $return['msg'] = $e->getMessage();
            }
            return $return;
        })

        //根据工号权限获取加班类型，用于hcm-api>快捷工具>修改考勤记录
        ->withCallback('getOvertimeTypeList', function ($locale, $param) use($timezone) {
            $this->locale = $locale['locale'];
            try{
                $ot_server = $this->class_factory('OvertimeServer',$this->locale);
                $data = $ot_server->getOvertimeTypeList($param);
                return ['code' => 1, 'msg' => '', 'data' => $data];
            }catch (\Exception $e){
                return ['code' => -1, 'msg' => $e->getMessage()];
            }
        })

        //获取加班类型，用于hcm-api>快捷工具>修改考勤记录 目前只有马来用 其他国家没有这接口
        ->withCallback('newStaffOvertimeDuration', function ($locale, $param) use($timezone) {
            $this->locale = $locale['locale'];
            $param['is_hcm'] = true;
            $ot_server = $this->class_factory('OvertimeServer', $this->locale);
            $data = $ot_server->overtimeDurationByShift($param);
            return $data;
        })

        //ot 列表页
        ->withCallback('overtimeList', function ($locale, $param) use($timezone) {
            $return['code'] = -1;
            $return['msg'] = '';
            try{
                $this->locale = $locale['locale'];
                $ot_server = $this->class_factory('OvertimeServer',$locale['locale']);
                $param['is_bi'] = 1;
                $data = $ot_server->overtime_list($param);
                return $data;
            }catch (\Exception $e){
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })
        //修改ot
        ->withCallback('editOvertime', function ($locale, $param) use($timezone) {
            $return['code'] = -1;
            $return['msg'] = '';
            try{
                $this->locale = $locale['locale'];
                $ot_server = $this->class_factory('OvertimeServer',$locale['locale']);
                $param['is_bi'] = 1;
                $this->getDI()->get('logger')->write_log("editOvertime 参数列表 " . json_encode($param, JSON_UNESCAPED_UNICODE), 'info');
                $data = $ot_server->edit_overtime($param);
                return $data;
            }catch (\Exception $e){
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })
        //撤销 OT
        ->withCallback('cancelOvertime', function ($locale, $param) use($timezone) {
            $return['code'] = -1;
            $return['msg'] = '';
            try{
                $this->locale = $locale['locale'];
                $ot_server = $this->class_factory('OvertimeServer',$locale['locale']);
                $param['is_bi'] = 1;
                $this->logger->info("cancelOvertime 参数列表 " . json_encode($param));
                return  $ot_server->cancelV3($param);
            }catch (\Exception $e){
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })
        // 获取all加班类型
        ->withCallback('allOverTimeTypes', function ($locale, $param) use($timezone) {
            $this->locale = $locale['locale'];
            $ot_server = $this->class_factory('OvertimeServer', $this->locale);
            return $ot_server->getApprovalOtType();
        })
        //考勤信息
        ->withCallback('attendanceInfo', function ($locale, $param) use($timezone) {
            $return['code'] = -1;
            $return['msg'] = '';
            try{
                $staff_id = intval($param['staff_info_id']);
                $date = date('Y-m-d',strtotime($param['attendance_date']));
                $att_server = new AttendanceServer($locale['locale'], $timezone);
                $data[0] = $att_server->getAttendanceByDate($staff_id,$date);
                return $data;
            }catch (\Exception $e){
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })

        //马来新班次 工具获取补卡类型
        ->withCallback('reissueTypeBook', function ($locale, $param) use($timezone) {
            $typeParam['user_info']['id'] = $param['staff_id'];
            $typeParam['reissue_date'] = $param['reissue_date'];
            $server = $this->class_factory('AuditServer',$locale['locale']);
            $data = $server->getReissueType($typeParam);
            return ['code' => 1, 'msg' => '', 'data' => $data];
        })

        //修改考勤记录
        ->withCallback('editAttendance', function ($locale, $param) use($timezone) {
            try{
                $server = $this->class_factory('AttendanceServer',$locale['locale']);
                $param['is_bi'] = 1;
                $data = $server->edit_att($param);
                return $data;
            }catch (\Exception $e){
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })

        //查询员工请假工具
        ->withCallback('leave_list', function ($locale, $param) {
            try{
                $model = new AuditRepository($locale['locale']);
                $data = $model->get_leave_by_month($param['staff_info_id'],$param);
                $return['code'] = 1;
                $return['data'] = $data;
                return $return;
            }catch (\Exception $e){
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })
        //员工 入职时间 年假额度
        ->withCallback('years_holiday', function ($locale, $param) {
            try{
                $model = new AuditRepository($locale['locale']);
                $data = $model::$years_holiday_left;
                $return['code'] = 1;
                $return['data'] = $data;
                return $return;
            }catch (\Exception $e){
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })

        //出差类型
        ->withCallback('trip_traffic_type', function ($locale, $param) use($timezone) {
            try{
                $model = new BusinesstripRepository($locale['locale'],$timezone);
                $data = $model->getTransportationType();
                $return['code'] = 1;
                $return['data'] = $data;
                return $return;
            }catch (\Exception $e){
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })

        //获取审批列表
        ->withCallback('get_audit_list', function ($locale, $param) use($timezone) {
            try{
                $server = new AuditListServer($locale['locale'], $timezone);
                $returnArr = $server->getAuditListByTypeV2($param);
                return $returnArr;

            } catch (\Exception $e){
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })
        //FOR OA wms、asset待审批count
        ->withCallback('get_audit_pending_count', function ($locale, $params) use($timezone) {
            $logger = $this->getDI()->get('logger');
            $logger->write_log("svc get_audit_pending_count 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
            try {
                $server = new AuditListServer($locale['locale'], $timezone);
                $data = $server->getAuditListPendingCount($params);
                $logger->write_log("svc get_audit_pending_count 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                return $server->checkReturn(['data' =>$data]);
            } catch (\Exception $e) {
                if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                    $logger->write_log("svc get_audit_pending_count 异常信息:" . $e->getMessage(), 'info');
                } else {
                    $logger->write_log("svc get_audit_pending_count 异常信息:" . $e->getMessage(), 'notice');
                }
                $return['data'] = [];
                $return['code'] = -1;
                $return['msg']  = $e->getMessage();
                return $return;
            }
        })

        //审批物料
        ->withCallback('edit_wms', function ($locale, $param) use($timezone) {
            try{
                $server = new WmsServer($locale['locale'], $timezone);
                $wmsInfo = (new WmsRepository())->getWmsOrderRById($param['audit_id']);

                $userinfo = [
                    'id'                => $param['id'],
                    'status'            => $param['status'],
                    'reject_reason'     => $param['reject_reason'],
                    'audit_id'          => $wmsInfo['order_id'] ?? 0,
                    'name'              => $param['name'],
                    'organization_name' => $param['organization_name'],
                ];
                $param['audit_id'] = $wmsInfo['order_id'] ?? 0;
                $param['order_id'] = $wmsInfo['order_id'] ?? 0;
                $result = $server->updateWmsOrder($param, $userinfo);
                return $result;
            }catch (\Exception $e){
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })
        //审批资产申请
        ->withCallback('edit_asset', function ($locale, $param) use($timezone) {
            try{
                $server = new AssetServer($locale['locale'], $timezone);
                $userinfo = [
                    'organization_name' => $param['organization_name'],
                ];
                [$result, $msg] = $server->updateAssetInfo($param, $userinfo);
                if ($result) {
                    $return = [
                        'code'  => 1,
                        'msg'   => '',
                        'data'  => $result
                    ];

                } else {
                    $return = [
                        'code'  => -1,
                        'msg'   => $msg,
                        'data'  => ''
                    ];
                }
                return $return;
            }catch (\Exception $e){
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })
        //获取审批详情
        ->withCallback('get_audit_detail', function ($locale, $param) use($timezone) {
            try{
                $server = new AuditListServer($locale['locale'], $timezone);
                $returnArr = $server->getAuditDetailByType($param);
                return $returnArr;
            }catch (\Exception $e){
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })

        //推送消息
        ->withCallback('sendMessageToSubmitter', function ($locale, $param) {
            try{
                if (!$param || !$param['staff_id'] || !$param["message_title"] || !$param["message_content"] || !$param["type"]){
                    return false;
                }
                $param = [
                    'staff_info_id' => $param['staff_id'],
                    'message_title' => $param["message_title"],
                    'message_content'=> $param["message_content"],
                    'type'          => $param["type"]
                ];
                $returnArr = (new PublicRepository())->pushAndSendMessageToSubmitter($param);
                return $returnArr;
            }catch (\Exception $e){
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })
        //获取指定工号的离职申请信息
        ->withCallback('get_resign_detail', function ($locale, $param) use($timezone)  {
            try{
                $repo = new ResignRepository($locale['locale'], $timezone);
                $return = [
                    'code'  => 1,
                    'msg'   => '',
                    'data'  => $repo->getResignBySubmitterId($param)
                ];
                return $return;
            }catch (\Exception $e){
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })

        //bi工具 撤销操作接口
        ->withCallback('leave_cancel', function ($locale, $param) use($timezone) {
            try{
                $return['code'] = -1;
                $return['msg'] = '';
                if (!$param || !$param['staff_id'] || !$param["audit_id"] ){
                    $return['msg'] = 'wrong parameter';
                    return $return;
                }

                $param = [
                    'staff_id' => $param['staff_id'],
                    'audit_id'=> $param["audit_id"],
                    'reject_reason' => 'system tool cancel',
                    'status' => enums::$audit_status['revoked'],
                    'operate_id' => $param['operate_id'] ?? 0,
                    'is_bi'   => 1
                ];

                $server = $this->class_factory('AuditServer',$locale['locale']);
                $return = $server->auditEditStatus($param);
                return $return;

            }catch (\Exception $e){
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })
        //bi 紧急事故列表 事故类型接口
        ->withCallback('emergency_type', function ($locale, $param) use($timezone) {
            try{
                $server = new BackyardServer($locale['locale'],$timezone);
                $returnArr = $server -> get_emergency_type();
                return $returnArr;

            }catch (\Exception $e){
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })
        //bi 紧急事故列表 个人类型接口
        ->withCallback('personal_type', function ($locale, $param) use($timezone) {
            try{
                $server = new BackyardServer($locale['locale'],$timezone);
                $returnArr = $server -> get_personal_type();
                return $returnArr;
            }catch (\Exception $e){
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })
        //同步FBI系统上级变更
        ->withCallback('sync_changed_superior', function ($locale, $param) use($timezone) {
            $this->getDI()->get('logger')->write_log("svc sync_changed_superior 参数列表 locale:" . json_encode($locale) . ";param:" . json_encode($param), 'info');

            try{
                $return['code'] = -1;
                $return['msg'] = '';
                if (!$param || !$param['staff_info_id']){
                    $return['msg'] = 'wrong parameter';
                    return $return;
                }

                $params = [
                    'staff_info_id'  => $param['staff_info_id'],
                    'before_manager' => $param['before_manager'],
                    'after_manager'  => $param['after_manager'],
                ];

                $return = (new AuditListServer($locale['locale'],$timezone))->replaceSuperior($params);
                $this->getDI()->get('logger')->write_log("svc sync_changed_superior 数据返回:" . json_encode($return), 'info');
                return $return;
            } catch (\Exception $e) {
                $this->getDI()->get('logger')->write_log("svc sync_changed_superior 异常信息:" . $e->getMessage(), 'error');
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })
            // 员工贷风控数据源
         ->withCallback('staff_loan', function ($locale, $param) use ($timezone) {

             try {

                 $attendanceServer = new AttendanceServer($locale['locale'], $timezone);
                 $result = $attendanceServer->staffLoansData($param['staff_id'], $param['month'] ?? '', $param['days'] ?? [], $param['start_date'] ?? '', $param['end_date'] ?? '');

                 return $result;
             } catch (\Exception $e) {
                 $this->getDI()->get('logger')->write_log("svc staff_loan 异常信息:" . $e->getFile() . $e->getMessage() . $e->getLine(), 'error');
                 $return['code'] = -1;
                 $return['msg'] = $e->getMessage();
                 return $return;
             }

        })

            // 员工贷风控数据  员工注册
        ->withCallback('staff_loan_register', function ($locale, $param) use ($timezone) {

            try {
                $attendanceServer = new AttendanceServer($locale['locale'], $timezone);
                $result = $attendanceServer->staffInfo($param['staff_id']);


                return $result;
            } catch (\Exception $e) {
                $this->getDI()->get('logger')->write_log("svc staff_loan_register 异常信息:" . $e->getMessage(), 'error');
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })

         ->withCallback('ticket_list', function ($locale, $param) use($timezone) {
            try{
                $userInfo = [
                    'organization_type' => $param['organization_type'],
                    'organization_id'   =>$param['organization_id'],
                    'id'=>$param['user_id'],
                    'name'=>$param['user_name']
                ];
                unset($param['user_id'],$param['organization_id'],$param['organization_type'],$param['user_name']);

                $server = new TicketServer($locale['locale'], $timezone, $userInfo);
                $res = $server->list($param,true,1);


                if ($res['code'] == 1) {
//                    $storeIdArr = array_column($res['data']['dataList'], 'created_store_id');
//                    $storeIdArr = array_unique($storeIdArr);

//                    $storeList = (new SysStoreServer())->getStoreName($storeIdArr);
                    if(!empty($res['data']['dataList'])){
                        $store_category = enums::$store_category;

                        foreach($res['data']['dataList'] as $k=>$v){
//                            //网点名称 从业务表取
//                            if($v['created_store_id'] == '-1'){
//                                $store_name = "Head Office";
//                            }else{
//                                $store_name = $v['store_name'];
//                            }
//                            $res['data']['dataList'][$k]['created_store_name'] = $store_name;

                            //网点类型
                            $res['data']['dataList'][$k]['category_name'] = '';
                            if(!empty($v['category']) && in_array($v['category'],array_keys($store_category))){
                                $res['data']['dataList'][$k]['category_name'] = $store_category[$v['category']];
                            }
                        }
                    }

                    $return = [
                        'code'  => 1,
                        'msg'   => '',
                        'data'  => $res['data']
                    ];

                } else {
                    $return = [
                        'code'  => -1,
                        'msg'   => $res['msg'],
                        'data'  => ''
                    ];
                }
                return $return;
            }catch (\Exception $e){
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })->withCallback('ticket_detail', function ($locale, $param) use($timezone) {
            try {
                $userInfo = [
                    'organization_type' => $param['organization_type'],
                    'organization_id' => $param['organization_id'],
                    'id' => $param['user_id'],
                    'name' => $param['user_name']
                ];
                unset($param['user_id'], $param['organization_id'], $param['organization_type'], $param['user_name']);

                $server = new TicketServer($locale['locale'], $timezone, $userInfo);
                $res = $server->detail($param['id'], true);

                if ($res['code'] == 1) {
                    $return = [
                        'code' => 1,
                        'msg' => '',
                        'data' => $res['data']
                    ];

                } else {
                    $return = [
                        'code' => -1,
                        'msg' => $res['msg'],
                        'data' => ''
                    ];
                }
                return $return;
            } catch (\Exception $e) {
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })->withCallback('ticket_item_type', function ($locale, $param) use($timezone) {
            try {
                $userInfo = [
                    'organization_type' => $param['organization_type'],
                    'organization_id' => $param['organization_id'],
                    'id' => $param['user_id'],
                    'name' => $param['user_name']
                ];
                unset($param['user_id'], $param['organization_id'], $param['organization_type'], $param['user_name']);
                $server = new TicketServer($locale['locale'], $timezone, $userInfo);
                $res = $server->getItemType();
                if ($res['code'] == 1) {
                    $return = [
                        'code' => 1,
                        'msg' => '',
                        'data' => $res['data']
                    ];
                }else{
                    $return = [
                        'code' => -1,
                        'msg' => $res['msg'],
                        'data' => ''
                    ];
                }
            } catch (\Exception $e) {
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
            }
            return $return;
        })->withCallback('ticket_reply', function ($locale, $param) use($timezone) {
            try {
                $userInfo = [
                    'organization_type' => $param['organization_type'],
                    'organization_id' => $param['organization_id'],
                    'id' => $param['user_id'],
                    'name' => $param['user_name']
                ];
                unset($param['user_id'], $param['organization_id'], $param['organization_type'], $param['user_name']);

                $server = new TicketServer($locale['locale'], $timezone, $userInfo);
                $res = $server->reply($param);

                if ($res['code'] == 1) {
                    $return = [
                        'code' => 1,
                        'msg' => '',
                        'data' => $res['data']
                    ];

                } else {
                    $return = [
                        'code' => -1,
                        'msg' => $res['msg'],
                        'data' => ''
                    ];
                }
                return $return;
            } catch (\Exception $e) {
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })->withCallback('ticket_batch_close', function ($locale, $param) use($timezone) {
            try {
                $userInfo = [
                    'organization_type' => $param['organization_type'],
                    'organization_id' => $param['organization_id'],
                    'id' => $param['user_id'],
                    'name' => $param['user_name']
                ];
                unset($param['user_id'], $param['organization_id'], $param['organization_type'], $param['user_name']);

                $server = new TicketServer($locale['locale'], $timezone, $userInfo);
                $res = $server->batchClose($param);

                if ($res['code'] == 1) {
                    $return = [
                        'code' => 1,
                        'msg' => '',
                        'data' => $res['data']
                    ];

                } else {
                    $return = [
                        'code' => -1,
                        'msg' => $res['msg'],
                        'data' => ''
                    ];
                }
                return $return;
            } catch (\Exception $e) {
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })

        //bi工具 离职撤销
        ->withCallback('resign_cancel', function ($locale, $param) use($timezone) {
            try{
                $return['code'] = -1;
                $return['msg'] = '';
                if (!$param || !$param['staff_info_id'] || (!$param["resign_id"] && !$param['id']) || !$param['operator_id']||!$param['operator_name']||!$param['reason'] ){
                    $return['msg'] = 'wrong parameter';
                    return $return;
                }

                if ($param['type'] == AuditListEnums::APPROVAL_TYPE_COMPANY_TERMINATION_CONTRACT) {
                    //更新撤销原因、离职申请审批状态
                    $paramData = [
                        'status'        => enums::$audit_status['revoked'],
                        'cancel_reason' => $param['reason'],
                        'staff_id'      => $param['operator_id'],  //操作人
                        'staff_name'    => $param['operator_name'],//操作人姓名
                        'audit_id'      => $param['id'],           //id
                    ];
                    $server    = new CompanyTerminationContractServer($locale['locale'], $timezone);
                    $server    = Tools::reBuildCountryInstance($server, [$locale['locale'], $timezone]);
                    $server->cancel($paramData);
                    $return['code'] = 1;
                    $return['msg']  = 'ok';
                    return $return;
                }
                //更新撤销原因、离职申请审批状态
                $paramData = [
                    'resign_id'     => $param['resign_id'],
                    'status'        => enums::$audit_status['revoked'],
                    'cancel_reason' => $param['reason'],
                    'staff_id'      => $param['operator_id'], //操作人
                    'staff_name'    => $param['operator_name'],//操作人姓名
                    'audit_id'      => $param['resign_id'],//id
                ];

               if ($param['type'] == AuditListEnums::APPROVAL_TYPE_CANCEL_CONTRACT) {
                    $server = new CancelContractServer($locale['locale'], $timezone);
                    $server = Tools::reBuildCountryInstance($server, [$locale['locale'], $timezone]);
                    $result = $server->cancel($paramData);
                } else {
                    $server = new ResignServer($locale['locale'], $timezone);
                    $server = Tools::reBuildCountryInstance($server, [$locale['locale'], $timezone]);
                    $result = $server->cancelResign($paramData);
                }
                if($result){
                    $return['code'] = 1;
                    $return['msg'] ='ok';
                }else{
                    $return['msg'] = 'fail';
                }
                return $return;
            }catch (\Exception $e){
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })
        //获取审批详情
        ->withCallback('getDetailForHcm', function ($locale, $param) use($timezone) {
            $this->getDI()->get('logger')->write_log("svc getDetail 参数列表 locale:" . json_encode($locale) . ";param:" . json_encode($param), 'info');
            try{
                $server = new AuditListServer($locale['locale'], $timezone);
                $param['version'] = empty($param['version']) ? 3 : $param['version'];//默认 版本3 hcm 和by 用4 其他地方先不变
                $returnArr = $server->getAuditDetailByType($param);
                $this->getDI()->get('logger')->write_log("svc getDetail 数据返回:" . json_encode($returnArr), 'info');
                return $returnArr;
            }catch (\Exception $e){
                $this->getDI()->get('logger')->write_log("svc getDetail 异常信息:" . $e->getMessage(), 'error');
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })

        // win_hr 薪资审批判断是否需要审批
        ->withCallback('salary_can_approve', function ($locale, $params) use($timezone) {
            try {
                // 添加薪资审批
                $this->getDI()->get("logger")->write_log(json_encode(['locale' => $locale, 'params'=> $params]), "info");

                    return (new SalaryServer($locale['locale'], $timezone))->isCanApproval_v2(
                        $params['resume_id'],
                        $params['job_id'],
                        $params['userinfo'],
                        (int)$params['money'],
                        (int)$params['trial_salary'],
                        (int)$params['renting'],
                        isset($params['in_salary_range']) ? (int)$params['in_salary_range'] : 1, // 是否在薪资范围内
                        isset($params['position_allowance']) ? (int)$params['position_allowance'] : 0, // 岗位津贴
                        isset($params['deminimis_benefits']) ? (int)$params['deminimis_benefits'] : 0, //
                        isset($params['other_non_taxable_allowance']) ? (int)$params['other_non_taxable_allowance'] : 0, //
                        isset($params['xinghuo_allowance']) ? (int)$params['xinghuo_allowance'] : 0, // 星火激励津贴
                        isset($params['currency']) ? (int)$params['currency'] : SalaryApproveModel::CURRENCY_THB, // 币种,后续迁移到winhr 这个方法太不规范
                        isset($params['subsidy_type']) ? (int)$params['subsidy_type'] : SalaryApproveModel::SUBSIDY_TYPE_NOT_SELECT// 补贴
                    );

            } catch (\Exception $e) {
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })
        // win_hr 薪资审批判断是否需要审批 第二版
        ->withCallback('salary_can_approve_v2', function ($locale, $params) use($timezone) {
            try {
                // 添加薪资审批
                $this->getDI()->get("logger")->write_log(json_encode(['locale' => $locale, 'params'=> $params]), "info");

                return (new SalaryServer($locale['locale'], $timezone))->isCanApproval_v4(
                    $params['resume_id'],
                    $params['job_id'],
                    $params['userinfo'],
                    $params['money'],
                    $params['trial_salary']
                );

            } catch (\Exception $e) {
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })
        // win_hr 薪资审批 添加薪资审批 薪资申请
        ->withCallback('salary_add_approve', function ($locale, $params) use($timezone) {
            try {
                // 添加薪资审批
                $this->getDI()->get("logger")->write_log(json_encode(['locale' => $locale, 'params' => $params]), "info");
                return (new SalaryServer($locale['locale'], $timezone))->addApprove_v2($params);
            } catch (\Exception $e) {
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })
            ->withCallback('salary_add_approve_v2', function ($locale, $params) use($timezone) {
                try {
                    // 添加薪资审批
                    $this->getDI()->get("logger")->write_log(json_encode(['locale' => $locale, 'params' => $params]), "info");

                    return (new SalaryServer($locale['locale'], $timezone))->addApprove_v4(
                        $params['resume_id'] ?? 0,
                        $params['job_id'] ?? 0,
                        $params['userinfo'] ?? [],
                        isset($params['job_title_grade']) ? (int)$params['job_title_grade'] : 0,
                        isset($params['current_salary']) ? (int)$params['current_salary'] : 0,
                        isset($params['in_salary_range']) ? (int)$params['in_salary_range'] : 1, // 是否在薪资范围内
                        $params['work_days'] ?? 0,
                        isset($params['company']) ? (string)$params['company'] : '', // 公司
                        $params['money'] ?? 0,
                        $params['trial_salary'] ?? 0
                    );
                } catch (\Exception $e) {
                    $return['code'] = -1;
                    $return['msg'] = $e->getMessage();
                    return $return;
                }
            })
            ->withCallback('approve_salary_items', function ($locale, $params) use($timezone) {
                try {
                    // 添加薪资审批
                    $this->getDI()->get("logger")->write_log(json_encode(['locale' => $locale, 'params' => $params]), "info");

                    return (new SalaryServer($locale['locale'], $timezone))->salaryItems(
                        $params['resume_id']
                    );
                } catch (\Exception $e) {
                    $return['code'] = -1;
                    $return['msg'] = $e->getMessage();
                    return $return;
                }
            })

            // win_hr 薪资审批 撤销薪资审批
        ->withCallback('salary_revoke_approve', function ($locale, $params) use($timezone) {
            try {
                // 撤销薪资审批
                $this->getDI()->get("logger")->write_log(json_encode(['locale' => $locale, 'params'=> $params]), "info");
                $salaryService = new SalaryServer($locale['locale'], $timezone);
                $info = $salaryService->getLastApprove($params['resume_id']);
                return $salaryService->updateApprove(
                    $params['userinfo'],
                    $info['id'],
                    SalaryServer::STATUS_REVOKED,
                    $params['revoke_remark']
                );
            } catch (\Exception $e) {
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })
        // win_hr 薪资审批 薪资审批详情
        ->withCallback('salary_approve_detail', function ($locale, $params) use($timezone) {
            try {
                // 薪资审批详情
                $this->getDI()->get("logger")->write_log(json_encode(['locale' => $locale, 'params'=> $params]), "info");
                return (new SalaryServer($locale['locale'], $timezone))->winHrApprovalDetail($params['userinfo'], $params['resume_id']);
            } catch (\Exception $e) {
                $this->getDI()->get("logger")->write_log("salary_approve_detail " . json_encode(['locale' => $locale, 'params'=> $params, "error_msg" => $e->getMessage(),
                        "error_line" => $e->getLine(),
                        "error_file" => $e->getFile(),
                        ], JSON_UNESCAPED_UNICODE), "error");
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                return $return;
            }
        })
        //续签合同审批详情
        ->withCallback('renew_contract_apply_detail', function ($locale, $params) use ($timezone) {
            try {
                $result = (new HrStaffRenewContractServer($locale['locale'], $timezone))->winHrApprovalDetail($params['userinfo'], $params['contract_id'], $params['version'] ?? 1);
                $this->getDI()->get("logger")->write_log([
                    'function' => 'renew_contract_apply_detail',
                    'locale'   => $locale,
                    'params'   => $params,
                    'result'   => $result,
                ], "info");
                return $result;
            } catch (\Exception $e) {
                $this->getDI()->get("logger")->write_log([
                    'function' => 'renew_contract_apply_detail',
                    'locale'   => $locale,
                    'params'   => $params,
                    "error_msg"  => $e->getMessage(),
                    "error_line" => $e->getLine(),
                    "error_file" => $e->getFile(),
                ], 'error');
                $return['code'] = -1;
                $return['msg']  = $e->getMessage();
                return $return;
            }
        })
        //撤销续签申请
        ->withCallback('revoke_renew_contract_apply', function ($locale, $params) use ($timezone) {
            try {
                $hrStaffRenewContractServer = new HrStaffRenewContractServer($locale['locale'], $timezone);
                $hrStaffRenewContractServer = Tools::reBuildCountryInstance($hrStaffRenewContractServer, [$locale['locale'], $timezone]);

                $result = $hrStaffRenewContractServer->winHrRevokeApply([
                    'userinfo' => $params['userinfo'],
                    'contract_id' => $params['contract_id'],
                    'revoke_remark' => $params['revoke_remark'] ?? ''
                ]);
                $this->getDI()->get("logger")->write_log([
                    'function' => 'revoke_renew_contract_apply',
                    'locale'   => $locale,
                    'params'   => $params,
                    'result'   => $result,
                ], "info");
                return $result;
            } catch (\Exception $e) {
                $this->getDI()->get("logger")->write_log([
                    'function' => 'revoke_renew_contract_apply',
                    'locale'   => $locale,
                    'params'   => $params,
                    "error_msg"  => $e->getMessage(),
                    "error_line" => $e->getLine(),
                    "error_file" => $e->getFile(),
                ], 'error');
                $return['code'] = -1;
                $return['msg']  = $e->getMessage();
                return $return;
            }
        })
        // 创建审批
        ->withCallback('create_hc', function ($locale, $params) use($timezone) {
            $logger = $this->getDI()->get('logger');
            $logger->write_log("svc create_hc 参数列表 locale:" . json_encode($locale) . ";param:" . json_encode($params), 'info');
            try {

                $server = $this->class_factory('HcServer', $locale['locale'], $timezone);
                $data   = $server->addHc($params);
                $logger->write_log("svc create_hc 数据返回:" . json_encode($data), 'info');
                return $data;
            } catch (\Exception $e) {
                if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                    $logger->write_log("svc create_hc 异常信息:" . $e->getMessage(), 'info');
                } else {
                    $logger->write_log("svc create_hc 异常信息:" . $e->getMessage(), 'notice');
                }
                $return['code'] = -1;
                $return['msg']  = $e->getMessage();
                return $return;
            }
        })
        // 审批同意
        ->withCallback('update_hc', function ($locale, $params) use($timezone) {
            $logger = $this->getDI()->get('logger');
            $logger->write_log("svc update_hc 参数列表 locale:" . json_encode($locale) . ";param:" . json_encode($params), 'info');
            try {

                $server = new HcServer($locale['locale'], $timezone);
                $data   = $server->updateHcV2($params);
                $logger->write_log("svc update_hc 数据返回:" . json_encode($data), 'info');
                return $data;
            } catch (\Exception $e) {
                $logger->write_log("svc update_hc 异常信息:" . $e->getMessage(), 'error');
                $return['code'] = -1;
                $return['msg']  = $e->getMessage();
                return $return;
            }
        })
        // hc申请 离职员工列表
        ->withCallback('get_hc_leave_staff_list', function ($locale, $params) use($timezone) {
            $logger = $this->getDI()->get('logger');
            $logger->write_log("svc get_hc_leave_staff_list 参数列表 locale:" . json_encode($locale) . ";param:" . json_encode($params), 'info');
            try {

                $server = new HcServer($locale['locale'], $timezone);
                $data   = $server->getHcStaffLeaveList($params);
                $logger->write_log("svc get_hc_leave_staff_list 数据返回:" . json_encode($data), 'info');

                $return['code'] = 1;
                $return['data']  = $data;
                return $return;
            } catch (\Exception $e) {
                $logger->write_log("svc get_hc_leave_staff_list 异常信息:" . $e->getMessage(), 'error');
                $return['code'] = -1;
                $return['msg']  = $e->getMessage();
                return $return;
            }
        })
        // hc申请 离职员工列表
        ->withCallback('get_hr_hc_leave_staff_list', function ($locale, $params) use($timezone) {
            $logger = $this->getDI()->get('logger');
            $logger->write_log("svc get_hr_hc_leave_staff_list 参数列表 locale:" . json_encode($locale) . ";param:" . json_encode($params), 'info');
            try {

                $server = new HcServer($locale['locale'], $timezone);
                $data   = $server->getHrHcLeaveStaffListByHcId($params['hc_id']);
                $logger->write_log("svc get_hr_hc_leave_staff_list 数据返回:" . json_encode($data), 'info');
                $return['code'] = 1;
                $return['data']  = $data;
                return $return;
            } catch (\Exception $e) {
                $logger->write_log("svc get_hr_hc_leave_staff_list 异常信息:" . $e->getMessage(), 'error');
                $return['code'] = -1;
                $return['msg']  = $e->getMessage();
                return $return;
            }
        })
        // 审批同意
        ->withCallback('get_hc_workflow', function ($locale, $params) use($timezone) {
            /**
             * 待废弃
             * @deprecated
             */
            $logger = $this->getDI()->get('logger');
            $logger->write_log("svc get_hc_workflow 参数列表 locale:" . json_encode($locale) . ";param:" . json_encode($params), 'info');
            try {
                $server = new ApprovalServer($locale['locale'], $timezone);
                $data   = $server->getAuditLogs($params['audit_id'], $params['audit_type'], $params['staff_id'], '', 1);
                $logger->write_log("svc get_hc_workflow 数据返回:" . json_encode($data), 'info');
                return $data;
            } catch (\Exception $e) {
                $logger->write_log("svc get_hc_workflow 异常信息:" . $e->getMessage(), 'error');
                $return['code'] = -1;
                $return['msg']  = $e->getMessage();
                return $return;
            }
        })
        // 审批流记录
        ->withCallback('get_audit_log', function ($locale, $params) use($timezone) {
            $logger = $this->getDI()->get('logger');
            $logger->write_log("svc get_audit_log 参数列表 locale:" . json_encode($locale) . ";param:" . json_encode($params), 'info');
            try {
                $server = new ApprovalServer($locale['locale'], $timezone);
                if (empty($params['log_version'])) {
                    $params['log_version'] = 3;
                }
                $data   = $server->getAuditLogs($params['audit_id'], $params['audit_type'], $params['staff_id'], '', $params['log_version']);
                $logger->write_log("svc get_audit_log 数据返回:" . json_encode($data), 'info');
                return $data;
            } catch (\Exception $e) {
                $logger->write_log("svc get_audit_log 异常信息:" . $e->getMessage(), 'error');
                $return['code'] = 0;
                $return['msg']  = $e->getMessage();
                return $return;
            }
        })
        ->withCallback('login', function ($locale,$param) {
            //账号密码登陆
            try{
                $this->getDI()->get('logger')->write_log($param,'info');

                $Login = new LoginUserServer();
                if(empty($param['account']) || empty($param['pwd'])){
                    $return['code'] = -1;
                    $return['msg'] = 'account or pwd empty';
                }
                $data = $Login->verify_fle($param);
                if($data){
                    $return['code'] = 1;
                    $return['data'] = $data;
                }else{
                    $return['code'] = -1;
                    $return['msg'] = 'account or Password error';
                }
                $this->getDI()->get('logger')->write_log($return,'info');

                return $return;
            }catch (\Exception $e){
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                $this->getDI()->get('logger')->write_log($return);
                return $return;
            }
        })
        ->withCallback('personinfo', function ($locale,$param) use($timezone) {
            //账号信息
            try{
                $this->getDI()->get('logger')->write_log($param,'info');

                $return['code'] = 0;
                $return['data'] = null;
                if(empty($param['account'])){
                    $return['code'] = -1;
                    $return['msg'] = 'account empty';
                    return $return;
                }

                $fle_rpc = (new ApiClient('fle','com.flashexpress.fle.svc.api.StaffAuthSvc','getStaffInfoByToken'));
                $fle_rpc->setParams($param['account']);
                $data_list = $fle_rpc->execute();
                if(isset($data_list['error'])){
                    $data_list['info'] = $data_list['error']['message'];
                    unset($data_list['error']);
                }if(isset($data_list['info']['code']) && $data_list['info']['code'] == '100112'){
                    $data_list['info'] = $data_list['info']['message'];
                }elseif(isset($data_list['result'])){
                    $this->getDI()->get('logger')->write_log($data_list,'info');
                    $staff_id = $data_list['result']['id'];
                    $staff =  (new PersoninfoServer($locale['locale'], $timezone))->getPerson(['staff_id' => $staff_id]);
                    if($staff['data']){
                        $return['code'] = 1;
                        $return['data'] = $staff['data'] ?? null;
                    }else{
                        $return['code'] = 0;
                        $return['data'] = null;
                    }
                }
                $this->getDI()->get('logger')->write_log($data_list,'info');

                return $return;
            }catch (\Exception $e){
                $return['code'] = -1;
                $return['msg'] = $e->getMessage();
                $this->getDI()->get('logger')->write_log($return);
                return $return;
            }
        })
        // 获取参考数据
        ->withCallback('get_hc_references', function ($locale, $params) use($timezone) {
            $logger = $this->getDI()->get('logger');
            $logger->write_log("svc get_hc_references 参数列表 locale:" . json_encode($locale) . ";param:" . json_encode($params), 'info');
            try {

                $server = $this->class_factory("HcServer", $locale['locale'], $timezone);
                $data   = $server->getHcReferences($params);
                $logger->write_log("svc get_hc_references 数据返回:" . json_encode($data), 'info');
                return $data;
            } catch (\Exception $e) {
                $logger->write_log("svc get_hc_references 异常信息:" . $e->getMessage(), 'error');
                $return['code'] = -1;
                $return['msg']  = $e->getMessage();
                return $return;
            }
        })
        // 获取职位列表
        ->withCallback('get_department_job_title_list', function ($locale, $params) use($timezone) {
            $logger = $this->getDI()->get('logger');
            $logger->write_log("svc get_department_job_title_list 参数列表 locale:" . json_encode($locale) . ";param:" . json_encode($params), 'info');
            try {

                $server = new HcServer($locale['locale'], $timezone);
                $data   = $server->getJobTitleList($params);
                $logger->write_log("svc get_department_job_title_list 数据返回:" . json_encode($data), 'info');
                return $data;
            } catch (\Exception $e) {
                $logger->write_log("svc get_department_job_title_list 异常信息:" . $e->getMessage(), 'error');
                $return['code'] = -1;
                $return['msg']  = $e->getMessage();
                return $return;
            }
        })
            // 获取待审批的count数
            ->withCallback('get_panding_count', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc get_panding_count 参数列表 locale:" . json_encode($locale) . ";param:" . json_encode($params), 'info');
                try {
                    if(empty($params['staff_info_id'])){
                        return 0;
                    }
                    //获取待审批数
                    $server = (new BackyardServer($locale['locale'], $timezone))->un_read_and_audit(
                        ['staff_id' => $params['staff_info_id']],
                        ['staff_id' => $params['staff_info_id']],
                        PushEnums::PUSH_OPT_SOURCE_SVC
                    );
                    $data = $server['un_audit_num'] ?? 0;
                    $logger->write_log("svc get_panding_count 数据返回:" . json_encode($data) . json_encode($params), 'info');
                    return $data;
                } catch (\Exception $e) {
                    $logger->write_log("svc get_panding_count 异常信息:" . $e->getMessage() . $e->getTraceAsString(), 'error');
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })

            // 消息审批创建
            ->withCallback('create_message_audit', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc create_message_audit 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');

                try {
                    $server = new MessageCenterServer($locale['locale'], $timezone);
                    $data   = $server->createWorkFlow($params);
                    $logger->write_log("svc create_message_audit 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');

                    return $data;

                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc create_message_audit 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc create_message_audit 异常信息:" . $e->getMessage(), 'notice');
                    }

                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })

            // 获取消息审批流日志
            ->withCallback('get_message_audit_stream', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc get_message_audit_stream 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');

                try {
                    $server = new MessageCenterServer($locale['locale'], $timezone);
                    $data   = $server->getAuditStream($params);
                    $logger->write_log("svc get_message_audit_stream 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');

                    return $data;

                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc get_message_audit_stream 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc get_message_audit_stream 异常信息:" . $e->getMessage(), 'notice');
                    }

                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            // 获取Quitclaim审批流日志
            ->withCallback('get_quitclaim_audit_stream', function ($locale, $params) use ($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc get_quitclaim_audit_stream 参数列表 locale:" . json_encode($locale,
                        JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');

                try {
                    $server = new QuitclaimServer($locale['locale'], $timezone);
                    $data   = $server->getAuditStream($params);
                    $logger->write_log("svc get_quitclaim_audit_stream 数据返回:" . json_encode($data,
                            JSON_UNESCAPED_UNICODE), 'info');

                    return $data;
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc get_quitclaim_audit_stream 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc get_quitclaim_audit_stream 异常信息:" . $e->getMessage(), 'notice');
                    }

                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            // 撤销消息审批
            ->withCallback('message_audit_cancel', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc message_audit_cancel 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');

                try {
                    $server = new MessageCenterServer($locale['locale'], $timezone);
                    $data   = $server->cancel($params);
                    $logger->write_log("svc message_audit_cancel 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $data;

                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc message_audit_cancel 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc message_audit_cancel 异常信息:" . $e->getMessage(), 'notice');
                    }

                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            // winhr合同删除后删除对应消息
            ->withCallback('delete_contract_msg', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc delete_contract_msg 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');

                try {
                    $return  = HrStaffContractServer::getInstance($locale['locale'],$timezone)->changeMsgReadStatus($params['msg_id']);
                    return $return;

                } catch (\Exception $e) {

                    $logger->write_log("svc delete_contract_msg 异常信息:" . $e->getMessage(), 'notice');
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })

            ->withCallback('save_interior_orders_scm', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc save_interior_orders_scm参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $interiorOrderServer = new InteriorOrderServer($locale['locale'], $timezone);
                    $return = $interiorOrderServer->recommit_interior_order($params);
                    return $return;
                } catch (\Exception $e) {
                    $logger->write_log("svc save_interior_orders_scm 异常信息:" . $e->getMessage(), 'notice');
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            ->withCallback('package_apply_add_order', function ($locale, $params){
                $logger = $this->getDI()->get('logger');
                $logger->write_log("package_apply_add_order 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $packageServer = $this->class_factory('PackageServer',$locale['locale']);
                    $return = $packageServer->packageApplyAddOrder($params);
                    return $return;
                } catch (\Exception $e) {
                    $logger->write_log("package_apply_add_order 异常信息:" . $e->getMessage(), 'notice');
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            ->withCallback('package_apply_cancel', function ($locale, $params){
                $logger = $this->getDI()->get('logger');
                $logger->write_log("package_apply_cancel 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $packageServer = $this->class_factory('PackageServer',$locale['locale']);
                    $return = $packageServer->packageApplyCancel($params);
                    return $return;
                } catch (\Exception $e) {
                    $logger->write_log("package_apply_cancel 异常信息:" . $e->getMessage(), 'notice');
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            ->withCallback('package_apply_return_add_order', function ($locale, $params){
                $logger = $this->getDI()->get('logger');
                $logger->write_log("package_apply_return_add_order 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $packageReturnServer = $this->class_factory('PackageReturnServer',$locale['locale']);
                    $return = $packageReturnServer->packageApplyAddOrder($params);
                    return $return;
                } catch (\Exception $e) {
                    $logger->write_log("package_apply_return_add_order 异常信息:" . $e->getMessage(), 'notice');
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            ->withCallback('package_apply_return_cancel', function ($locale, $params){
                $logger = $this->getDI()->get('logger');
                $logger->write_log("package_apply_return_cancel 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $packageReturnServer = $this->class_factory('PackageReturnServer',$locale['locale']);
                    $return = $packageReturnServer->packageApplyCancel($params);
                    return $return;
                } catch (\Exception $e) {
                    $logger->write_log("package_apply_return_cancel 异常信息:" . $e->getMessage(), 'notice');
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            //oa 审批驳回 by申请 油费报销单
            ->withCallback('reject_fuel', function ($locale, $params) use($timezone)  {
                $staff_id = $params['staff_id'];
                $re_no = $params['no'];
                $server = new FuelBudgetServer($locale['locale'],$timezone);
                $flag = $server->reject_fuel($staff_id,$re_no);
                if(!$flag){
                    $this->getDI()->get('logger')->write_log("svc reject_fuel 删除失败:" .json_encode($params), 'info');
                }
                return $flag;
            })
            //创建处罚 申诉申请
            ->withCallback('create_penalty_appeal_audit', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc create_penalty_appeal_audit 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new PenaltyAppealServer($locale['locale'], $timezone);
                    $data   = $server->create($params);
                    $logger->write_log("svc create_penalty_appeal_audit 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');

                    return $data;
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc create_penalty_appeal_audit 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc create_penalty_appeal 异常信息:" . $e->getMessage(), 'notice');
                    }
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            ->withCallback('update_penalty_appeal_audit', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc update_penalty_appeal_audit 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new PenaltyAppealServer($locale['locale'], $timezone);
                    $data   = $server->update($params);
                    $logger->write_log("svc update_penalty_appeal_audit 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');

                    return $data;
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc update_penalty_appeal_audit 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc update_penalty_appeal 异常信息:" . $e->getMessage(), 'notice');
                    }
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            ->withCallback('close_penalty_appeal_audit', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc close_penalty_appeal_audit_close 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new PenaltyAppealServer($locale['locale'], $timezone);
                    $data   = $server->close($params);
                    $logger->write_log("svc close_penalty_appeal__audit_close 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $data;
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc close_penalty_appeal_audit_close 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc close_penalty_appeal_audit_close 异常信息:" . $e->getMessage(), 'notice');
                    }
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            ->withCallback('holiday_nums', function ($locale, $params) use($timezone) {
//                $auditServer = new AuditServer($locale['locale'], $timezone);
                $auditServer = $this->class_factory('AuditServer',$locale['locale']);
                try {
                    $this->getDI()->get('logger')->write_log('svc_holiday_nums' . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                    $staffIds = $params['staff_ids'];
                    $results = [];
                    $staff_model          = new StaffRepository();
                    $userInfos = $staff_model->getStaffsPosition($staffIds);

                    $userInfos = array_column($userInfos, null, 'staff_info_id');
                    foreach ($staffIds as $staffId) {
                        if (isset($userInfos[$staffId])) {
                            //拼接 入参
                            $p['staff_id'] = $staffId;
                            $p['user_info'] = $userInfos[$staffId];
                            $p['is_svc'] = 1;

                            $data = $auditServer->get_left_holidays($p);
                            $this->getDI()->get('logger')->write_log('svc_holiday_nums' . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                            if(empty($data) || empty($data['data'])){
                                $results[$staffId] = array();
                                continue;
                            }

                            $data = array_column($data['data'], null, 'code');
                            $row = array();
                            foreach ($data as $da){
                                if(!empty($da['text']))
                                    $row[$da['code']] = array('text' => $da['text']);//没有额度显示
                                else
                                    $row[$da['code']] = array('day_limit' => $da['day_limit']??'0' , 'day_sub' => $da['day_sub']??'0');
                            }
                            $results[$staffId] = $row;
                            continue;
                        }
                    }
                    return $auditServer->checkReturn(['data' => $results]);

                } catch (\Exception $e) {
                    //这里好像走不进来
                    $this->getDI()->get('logger')->write_log('svc_holiday' . $e->getMessage() . $e->getTraceAsString(), 'info');
                    return $auditServer->checkReturn(-3);
                }

            })->withCallback('annual_detail', function ($locale, $params) use($timezone) {
                $leaveServer = $this->class_factory('LeaveServer',$locale['locale']);
                $params['is_svc'] = 1;
                $res = $leaveServer->getAnnualDetail($params);
                $this->getDI()->get('logger')->write_log('annual_detail ' . json_encode($params, JSON_UNESCAPED_UNICODE) . json_encode($res,JSON_UNESCAPED_UNICODE), 'info');
                return ['code' => 1,'message' => 'success','data' => $res];
            })->withCallback('store_staff_attendance_for_boss', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc store_staff_attendance_for_boss 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new StatisticsAttendanceServer($locale['locale'], $timezone);
                    $data   = $server->getStoreAttendance($params);
                    $logger->write_log("svc store_staff_attendance_for_boss 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $server->checkReturn(['data' =>$data]);
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc store_staff_attendance_for_boss 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc store_staff_attendance_for_boss 异常信息:" . $e->getMessage(), 'notice');
                    }
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            //根据网点id获取负责人信息
            ->withCallback('store_manager_info', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc store_manager_info 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new SysStoreServer();
                    $data   = $server->getStoreManagerByStoreId($params['store_id']);
                    $logger->write_log("svc store_manager_info 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $server->checkReturn(['data' =>$data]);
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc store_manager_info 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc store_manager_info 异常信息:" . $e->getMessage(), 'notice');
                    }
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })->withCallback('workStaffByStaffs', function ($locale, $param)  use($timezone) {
                try {
                    $return['code'] = 1;
                    $return['msg'] = '';
                    $return['data'] = $param;

                    return $return;
                } catch (\Exception $e) {
                    $return['code'] = -1;
                    $return['msg'] = $e->getMessage();
                    return $return;
                }
            })
            //根据消息 id 获取消息信息
            ->withCallback('get_message_info', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc get_message_info 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new BackyardServer($locale['locale'], $timezone);
                    $data = $server->msg_detail($params);
                    $logger->write_log("svc store_manager_info 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $data;
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc get_message_info 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc get_message_info 异常信息:" . $e->getMessage(), 'notice');
                    }
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            //根据消息 id 和用户 id 把消息变为已读
            ->withCallback('has_read_operation_by_staffid', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc has_read_operation_by_staffid 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new BackyardServer($locale['locale'], $timezone);
                    $data = $server->has_read_operation($params['msg_id']);
                    $logger->write_log("svc has_read_operation_by_staffid 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $server->checkReturn(['data' =>$data]);
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc has_read_operation_by_staffid 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc has_read_operation_by_staffid 异常信息:" . $e->getMessage(), 'notice');
                    }
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            //根据员工状态
            ->withCallback('StaffListStatus', function ($locale, $params) use($timezone) {
                try {
                    $logger = $this->getDI()->get('logger');
                    $logger->write_log("svc StaffListStatus 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                    $return['data'] = [];
                    $return['code'] = 1;
                    $return['msg']  = 'ok';
                    $data = (new StaffServer())->StaffListStatus($params);
                    $return['data'] = $data;
                    if(empty($data)){
                        $return['code'] = 0;
                    }
                    $logger->write_log("svc StaffListStatus 数据返回:" . json_encode($return, JSON_UNESCAPED_UNICODE), 'info');
                    return $return;
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc StaffListStatus 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc StaffListStatus 异常信息:" . $e->getMessage(), 'notice');
                    }
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            //HCM 获取可用的审批流list
            ->withCallback('getValidWorkflowList', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc getValidWorkflowList 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new WorkflowManagementServer($locale['locale'], $timezone);
	                $server = Tools::reBuildCountryInstance($server, [$locale['locale'], $timezone]);
                    $data = $server->getValidWorkflowList();
                    $logger->write_log("svc getValidWorkflowList 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $server->checkReturn(['data' => $data]);
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc getValidWorkflowList 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc getValidWorkflowList 异常信息:" . $e->getMessage(), 'notice');
                    }
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            //HCM 编辑审批流中的固定工号
            ->withCallback('editWorkflowSpecifyStaffInfoId', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc getValidWorkflowList 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new WorkflowManagementServer($locale['locale'], $timezone);
                    $data = $server->editSpecifyStaffInfoId($params);
                    $logger->write_log("svc getValidWorkflowList 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $server->checkReturn(['data' => $data]);
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc getValidWorkflowList 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc getValidWorkflowList 异常信息:" . $e->getMessage(), 'notice');
                    }
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            //HCM 编辑审批流中的固定工号
            ->withCallback('getWorkflowForHcm', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc getWorkflowForHcm 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new WorkflowManagementServer($locale['locale'], $timezone);
                    $data = $server->getWorkflowForHcm($params);
                    $logger->write_log("svc getWorkflowForHcm 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $server->checkReturn(['data' => $data]);
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc getWorkflowForHcm 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc getWorkflowForHcm 异常信息:" . $e->getMessage(), 'notice');
                    }
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            //创建驾驶证识别请求
            ->withCallback('createDrivingLicenseIdentifyRequest', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc createDrivingLicenseIdentifyRequest 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new VehicleServer($locale['locale'], $timezone);
                    $data = $server->createDrivingLicenseIdentifyRequest($params);
                    $logger->write_log("svc createDrivingLicenseIdentifyRequest 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $server->checkReturn(['data' => $data]);
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc createDrivingLicenseIdentifyRequest 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc createDrivingLicenseIdentifyRequest 异常信息:" . $e->getMessage(), 'notice');
                    }
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            //获取比对结果
            ->withCallback('getDrivingLicenseComparisonInfo', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc getDrivingLicenseComparisonInfo 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new VehicleServer($locale['locale'], $timezone);
                    $data = $server->getDrivingLicenseComparisonInfo($params);
                    $logger->write_log("svc getDrivingLicenseComparisonInfo 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $server->checkReturn(['data' => $data]);
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc getDrivingLicenseComparisonInfo 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc getDrivingLicenseComparisonInfo 异常信息:" . $e->getMessage(), 'notice');
                    }
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            ->withCallback('getStaffClockinInfo', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc getStaffClockinInfo 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new StaffServer($locale['locale'], $timezone);
                    $data = $server->getStaffClockinInfo($params);
                    $logger->write_log("svc getStaffClockinInfo 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $server->checkReturn(['data' => $data]);
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc getStaffClockinInfo 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc getStaffClockinInfo 异常信息:" . $e->getMessage(), 'notice');
                    }
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            ->withCallback('clearStaffClockin', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc clearStaffPhoto 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = "dev,training";
                    if(!in_array(RUNTIME, ['dev','test','tra'])){
                        return $return;
                    }
                    $server = new StaffServer($locale['locale'], $timezone);
                    $data = $server->clearStaffClockin($params);
                    $logger->write_log("svc clearStaffPhoto 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $server->checkReturn(['data' => $data]);
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc clearStaffPhoto 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc clearStaffPhoto 异常信息:" . $e->getMessage(), 'notice');
                    }
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            //申请offer签字
            ->withCallback('apply_offer_sign', function ($locale, $param)  use($timezone) {
                $this->getDI()->get("logger")->write_log(json_encode(['locale' => $locale, 'params'=> $param]), "info");
                try{

                    if (strtolower(env('country_code', 'Th')) == 'th') {

                        return (new OfferSignApproveServer($locale['locale'], $timezone))->addApprove($param);
                    } else if (strtolower(env('country_code', 'Th')) == 'ph') {

                        return (new \FlashExpress\bi\App\Modules\Ph\Server\OfferSignApproveServer($locale['locale'], $timezone))->addApprove($param);
                    } else if (strtolower(env('country_code', 'Vn')) == 'vn') {

                        return (new \FlashExpress\bi\App\Modules\Vn\Server\OfferSignApproveServer($locale['locale'], $timezone))->addApprove($param);
                    } else if (strtolower(env('country_code', 'Th')) == 'la') {

                        return (new \FlashExpress\bi\App\Modules\La\Server\OfferSignApproveServer($locale['locale'], $timezone))->addApprove($param);
                    } else if (strtolower(env('country_code', 'Th')) == 'my') {

                        return (new \FlashExpress\bi\App\Modules\My\Server\OfferSignApproveServer($locale['locale'], $timezone))->addApprove($param);
                    } else if (strtolower(env('country_code', 'Id')) == 'id') {

                        return (new \FlashExpress\bi\App\Modules\Id\Server\OfferSignApproveServer($locale['locale'], $timezone))->addApprove($param);
                    }
                } catch (\Exception $e) {
                    $return['code'] = -1;
                    $return['msg'] = $e->getMessage().'-'.$e->getTraceAsString();
                    $this->getDI()->get("logger")->write_log("apply_offer_sign: ".$return['msg'], "info");
                    return $return;
                }

            })
            //撤销 审批offer签字
            ->withCallback('revoke_offer_sign', function ($locale, $param)  use($timezone) {

                $this->getDI()->get("logger")->write_log(json_encode(['locale' => $locale, 'params'=> $param]), "info");
                try{

                    if(!isset($param['approve_state'])) {

                        throw new \Exception('params error');
                    }
                    //撤销：0：薪资审批撤销连带撤销offer签字，4：撤销offer签字
                    $approve_state = $param['approve_state'];

                    if (strtolower(env('country_code', 'Th')) == 'th') {

                        $offer_sign_server = new OfferSignApproveServer($locale['locale'], $timezone);
                        $info = $offer_sign_server->getLastApprove($param['resume_id']);
                        return $offer_sign_server->updateApprove(
                            ['id'=>$param['submitter_id']],
                            $info['id'],
                            $approve_state,
                            $param['revoke_remark'],
                            $param['resume_id']
                        );

                    } else if (strtolower(env('country_code', 'Th')) == 'ph') {

                        $offer_sign_server = new \FlashExpress\bi\App\Modules\Ph\Server\OfferSignApproveServer($locale['locale'], $timezone);
                        $info = $offer_sign_server->getLastApprove($param['resume_id']);
                        return $offer_sign_server->updateApprove(
                            ['id'=>$param['submitter_id']],
                            $info['id'],
                            enums::$audit_status['revoked'],
                            $param['revoke_remark']
                        );
                    } else if (strtolower(env('country_code', 'Th')) == 'vn') {

                        $offer_sign_server = new \FlashExpress\bi\App\Modules\Vn\Server\OfferSignApproveServer($locale['locale'], $timezone);
                        $info = $offer_sign_server->getLastApprove($param['resume_id']);
                        return $offer_sign_server->updateApprove(
                            ['id'=>$param['submitter_id']],
                            $info['id'],
                            enums::$audit_status['revoked'],
                            $param['revoke_remark']
                        );
                    }else if (strtolower(env('country_code', 'Th')) == 'la') {

                        $offer_sign_server = new \FlashExpress\bi\App\Modules\La\Server\OfferSignApproveServer($locale['locale'], $timezone);
                        $info = $offer_sign_server->getLastApprove($param['resume_id']);
                        return $offer_sign_server->updateApprove(
                            ['id'=>$param['submitter_id']],
                            $info['id'],
                            enums::$audit_status['revoked'],
                            $param['revoke_remark']
                        );
                    }else if (strtolower(env('country_code', 'Th')) == 'my') {

                        $offer_sign_server = new \FlashExpress\bi\App\Modules\My\Server\OfferSignApproveServer($locale['locale'], $timezone);
                        $info = $offer_sign_server->getLastApprove($param['resume_id']);
                        return $offer_sign_server->updateApprove(
                            ['id'=>$param['submitter_id']],
                            $info['id'],
                            enums::$audit_status['revoked'],
                            $param['revoke_remark']
                        );
                    }else if (strtolower(env('country_code', 'Th')) == 'id') {

                        $offer_sign_server = new \FlashExpress\bi\App\Modules\Id\Server\OfferSignApproveServer($locale['locale'], $timezone);
                        $info = $offer_sign_server->getLastApprove($param['resume_id']);
                        return $offer_sign_server->updateApprove(
                            ['id'=>$param['submitter_id']],
                            $info['id'],
                            enums::$audit_status['revoked'],
                            $param['revoke_remark']
                        );
                    }
                } catch (\Exception $e) {
                    $return['code'] = -1;
                    $return['msg'] = $e->getMessage().'-'.$e->getTraceAsString();
                    return $return;
                }

            })
            //获取offer审批详情
            ->withCallback('offer_sign_detail', function ($locale, $param) use($timezone) {
                try{
                    $server = new AuditListServer($locale['locale'], $timezone);
                    $params['resume_id'] = $param['resume_id'];
                    $params['staff_id'] = $param['submitter_id'];
                    $params['isCommit'] = 1;
                    $params['id'] = 'st_'.$param['offer_sign_id'];
                    $params['type'] = 35;//offer签字
                    $params['date_created'] = $param['date_created'] ?? '';//申请时间
                    $returnArr = $server->getAuditDetailByType($params);
                    return $returnArr;
                }catch (\Exception $e){
                    $return['code'] = -1;
                    $return['msg'] = $e->getMessage();
                    return $return;
                }
            })
            //获取各网点职位的数量
            ->withCallback('getStoreJobTitleNumberInfo', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc getStoreJobTitleNumberInfo 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new VehicleServer($locale['locale'], $timezone);
                    $data = $server->getStoreJobTitleNumberInfo($params);
                    $logger->write_log("svc getStoreJobTitleNumberInfo 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $server->checkReturn(['data' => $data]);
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc getStoreJobTitleNumberInfo 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc getStoreJobTitleNumberInfo 异常信息:" . $e->getMessage(), 'notice');
                    }
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })//ForJava 获取员工信息
            ->withCallback('getStaffInfo', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc getStaffInfo 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $query_field = $params['query_field'] ?? '*';
                    $server = new StaffServer($locale['locale'], $timezone);
                    $data = $server->getStaffInfo($params,$query_field);
                    $logger->write_log("svc getStaffInfo 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $data;
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc getStaffInfo 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc getStaffInfo 异常信息:" . $e->getMessage(), 'notice');
                    }
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })//ForJava获取最近30天入职员工工号
            ->withCallback('getStaffIdsByHasEntryDays', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc getStaffIdsByHasEntryDays 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new StaffServer($locale['locale'], $timezone);
                    $data = $server->getStaffIdsByHasEntryDays($params);
                    $logger->write_log("svc getStaffIdsByHasEntryDays 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $data;
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc getStaffIdsByHasEntryDays 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc getStaffIdsByHasEntryDays 异常信息:" . $e->getMessage(), 'notice');
                    }
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            ->withCallback('createCooperator', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc createCooperator 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new StaffServer($locale['locale'], $timezone);
                    $data = $server->createCooperator($params);
                    $logger->write_log("svc createCooperator 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $server->checkReturn(['data' =>$data]);
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc createCooperator 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc createCooperator 异常信息:" . $e->getMessage(), 'notice');
                    }
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            ->withCallback('getAuditType', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc getAuditType 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new ApprovalServer($locale['locale'], $timezone);
                    $data = $server->getAuditType($locale['locale']);
                    $logger->write_log("svc getAuditType 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $server->checkReturn(['data' =>$data]);
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc getAuditType 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc getAuditType 异常信息:" . $e->getMessage(), 'notice');
                    }
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            //FlashPay支付申请退款
            ->withCallback('interior_goods_flash_pay_refund', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc interior_goods_flash_pay_refund 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new FlashPayServer($locale['locale'], $timezone);
                    $data = $server->refund($params);
                    $logger->write_log("svc interior_goods_flash_pay_refund 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $server->checkReturn(['data' =>$data]);
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc interior_goods_flash_pay_refund 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc interior_goods_flash_pay_refund 异常信息:" . $e->getMessage(), 'notice');
                    }
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
	        ->withCallback('getEnumVehicle', function ($locale, $params) use($timezone) {
	        	//OA - 可视化审批流-车辆来源
		        $logger = $this->getDI()->get('logger');
		        $logger->write_log("svc getEnumVehicle 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
		        try {
			        $server = Tools::reBuildCountryInstance(new VehicleServer($locale['locale'], $timezone), [$locale['locale'], $timezone]);
			        $data = $server->getAllVehicleSource();
			        $logger->write_log("svc getEnumVehicle 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
			        return $server->checkReturn(['data' => $data]);
		        } catch (\Exception $e) {
			        if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
				        $logger->write_log("svc getEnumVehicle 异常信息:" . $e->getMessage(), 'info');
			        } else {
				        $logger->write_log("svc getEnumVehicle 异常信息:" . $e->getMessage(), 'notice');
			        }
			        $return['data'] = [];
			        $return['code'] = -1;
			        $return['msg']  = $e->getMessage();
			        return $return;
		        }
	        })
            ->withCallback('getStaffList', function ($locale, $params) use($timezone) {
                //fms 获取员工列表
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc getStaffList 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new StaffServer($locale['locale'], $timezone);
                    if(empty($params['mobile'])){
                        return $server->checkReturn(['code'=> ErrCode::VALIDATE_ERROR,'msg' =>'Please fill in mobile number']);
                    }
                    $params['columns'] = 'staff_info_id,name';// 查询字段
                    $data = $server->getStaffList($params);
                    $logger->write_log("svc getStaffList 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $data;
                } catch (\Exception $e) {

                    $logger->write_log("svc getStaffList 异常信息:" . $e->getMessage(), 'error');

                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            ->withCallback('visualization', function ($locale, $params) use($timezone) {
                //hcm调用，审批流预览下拉列表
                $logger = $this->getDI()->get('logger');
                $logger->write_log (
                    "svc visualization locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . "; param:" . json_encode($params, JSON_UNESCAPED_UNICODE),
                    'info'
                );
                try {
                    $workFlowObj = new WorkflowServer($locale['locale'],$this->timezone);
                    $list = $workFlowObj->getWorkflowRelateType();
                    $logger->write_log("svc visualization 数据返回:" . json_encode($list, JSON_UNESCAPED_UNICODE), 'info');
                    return ['data' => $list];
                } catch (\Exception $e) {
                    $logger->write_log("svc visualization 异常信息:" . $e->getMessage(), 'error');
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            ->withCallback('getPreview', function ($locale, $params) use($timezone) {
                //hcm调用，审批流预览下拉列表
                $logger = $this->getDI()->get('logger');
                $logger->write_log (
                    "svc getPreview locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . "; param:" . json_encode($params, JSON_UNESCAPED_UNICODE),
                    'info'
                );
                try {
                    $workFlowObj = new WorkflowServer($locale['locale'],$this->timezone);
                    $list = $workFlowObj->getPreview((int) $params['staff_id'], (int) $params['relate_type']);
                    $logger->write_log("svc getPreview 数据返回:" . json_encode($list, JSON_UNESCAPED_UNICODE), 'info');
                    return ['data' => $list];
                } catch (\Exception $e) {
                    $logger->write_log("svc getPreview 异常信息:" . $e->getMessage(), 'error');
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })->withCallback('queryStaffInfo', function ($locale, $params) use($timezone) {
                // fms 获取员工信息
                $logger = $this->getDI()->get('logger');
                $logger->write_log (
                    "svc queryStaffInfo locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . "; param:" . json_encode($params, JSON_UNESCAPED_UNICODE),
                    'info'
                );
                try {
                    $workFlowObj = new StaffServer($locale['locale'], $this->timezone);
                    $list = $workFlowObj->getStaffInfoV2($params);
                    $logger->write_log("svc queryStaffInfo 数据返回:" . json_encode($list, JSON_UNESCAPED_UNICODE), 'info');
                    return $workFlowObj->checkReturn(['data' =>$list]);

                } catch (\Exception $e) {
                    $logger->write_log("svc queryStaffInfo 异常信息:" . $e->getMessage(), 'error');
                    $return['data'] = null;
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            ->withCallback('svc_find_hrbp', function ( $locale, $params ) use ( $timezone ) {
                //查找 审批流  hrbp
                $logger = $this->getDI()->get('logger');
                $logger->write_log('svc svc_find_hrbp 参数列表 locale:' . json_encode($locale, JSON_UNESCAPED_UNICODE) . ';param:' . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server        = new WorkflowServer($locale['locale'], $timezone);
                    $department_id = $params['department_id'] ?? '';  //部门
                    $store_id      = $params['store_id'] ?? '';       // ['store_id'=>'网点 id']
                    if (empty($department_id)) {
                        return $server->checkReturn(['code' => ErrCode::VALIDATE_ERROR, 'msg' => '[department_id] params missing !']);
                    }
                    if (empty($store_id)) {
                        return $server->checkReturn(['code' => ErrCode::VALIDATE_ERROR, 'msg' => '[store_id] params missing !']);
                    }
                    $data['hrbp'] = $server->findHRBP($department_id, ['store_id' => $store_id]);
                    $logger->write_log('svc svc_find_hrbp 数据返回:' . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $server->checkReturn(['data' => $data]);
                } catch (\Exception $e) {

                    $logger->write_log('svc findHRBP 异常信息:' . $e->getMessage(), 'error');

                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            ->withCallback('fmsGetJurisdictionStoreList', function ( $locale, $params ) use ( $timezone ) {
                //fms 根据员工获取负责的网点列表
                $logger = $this->getDI()->get('logger');
                $logger->write_log('svc fmsGetJurisdictionStoreList 参数列表 locale:' . json_encode($locale, JSON_UNESCAPED_UNICODE) . ';param:' . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new SystemExternalApprovalServer($locale['locale'], $timezone);
                    $data   = $server->getJurisdictionStoreList($params);
                    $logger->write_log('svc fmsGetJurisdictionStoreList 数据返回:' . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $data;
                } catch (\Exception $e) {

                    $logger->write_log('svc fmsGetJurisdictionStoreList 异常信息:' . $e->getFile()
                        .' line '.$e->getLine()
                        .' message '.$e->getMessage()
                        .' trace '.$e->getTraceAsString(), 'error');

                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            ->withCallback('fmsGetStaffAttendanceNegative', function ($locale, $params) use ($timezone) {
                //fms 根据工号获取员工入职信息和人脸底片
                $logger = $this->getDI()->get('logger');
                $logger->write_log('svc fmsGetStaffAttendanceNegative 参数列表 locale:'.json_encode($locale,
                        JSON_UNESCAPED_UNICODE).';param:'.json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new AttendanceServer($locale['locale'], $timezone);
                    $data   = $server->getStaffAttendanceNegative($params);
                    $logger->write_log('svc fmsGetStaffAttendanceNegative 数据返回:'.json_encode($data,
                            JSON_UNESCAPED_UNICODE), 'info');
                    return $data;
                } catch (\Exception $e) {
                    $logger->write_log('svc fmsGetStaffAttendanceNegative 异常信息:'.$e->getFile()
                        .' line '.$e->getLine()
                        .' message '.$e->getMessage()
                        .' trace '.$e->getTraceAsString(), 'error');

                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            ->withCallback('getSvcReportEnum', function ( $locale, $params ) use ( $timezone ) {
                //获取所有的举报原因
                $logger = $this->getDI()->get('logger');
                $logger->write_log('svc getSvcReportEnum 参数列表 locale:' . json_encode($locale, JSON_UNESCAPED_UNICODE) . ';param:' . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new ReportRepository($locale['locale'], $timezone);
                    $data   = $server->dictReportResonAll();
                    $logger->write_log('svc getSvcReportEnum 数据返回:' . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    $return['data'] = $data['data'] ?? [];
                    $return['code'] = ErrCode::SUCCESS;
                    $return['msg']  = 'success';
                    return $return;
                } catch (\Exception $e) {

                    $logger->write_log('svc getSvcReportEnum 异常信息:' . $e->getFile()
                        .' line '.$e->getLine()
                        .' message '.$e->getMessage()
                        .' trace '.$e->getTraceAsString(), 'error');

                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            //FOR OA 资产工单-列表
            ->withCallback('asset_work_order_list', function ($locale, $params) use ($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc asset_work_order_list 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new AssetWorkOrderServer($locale['locale'], $timezone);
                    $data = $server->getListForOa($params);
                    $logger->write_log("svc asset_work_order_list 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $server->checkReturn(['data' =>$data]);
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc asset_work_order_list 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc asset_work_order_list 异常信息:" . $e->getMessage(), 'notice');
                    }
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            //FOR OA 资产工单-问题类型\问题所属仓\处理状态
            ->withCallback('asset_work_order_enums', function ($locale, $params) use ($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc asset_work_order_enums 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new AssetWorkOrderServer($locale['locale'], $timezone);
                    $data = $server->getEnumsForOa($params);
                    $logger->write_log("svc asset_work_order_enums 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $server->checkReturn(['data' =>$data]);
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc asset_work_order_enums 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc asset_work_order_enums 异常信息:" . $e->getMessage(), 'notice');
                    }
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            //FOR OA 资产工单-详情
            ->withCallback('asset_work_order_detail', function ($locale, $params) use ($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc asset_work_order_detail 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new AssetWorkOrderServer($locale['locale'], $timezone);
                    $data = $server->getDetailForOa($params);
                    $logger->write_log("svc asset_work_order_detail 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $server->checkReturn(['data' =>$data]);
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc asset_work_order_detail 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc asset_work_order_detail 异常信息:" . $e->getMessage(), 'notice');
                    }
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            //FOR OA 资产工单-回复
            ->withCallback('asset_work_order_reply', function ($locale, $params) use ($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc asset_work_order_reply 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new AssetWorkOrderServer($locale['locale'], $timezone);
                    $data = $server->replyForOa($params);
                    $logger->write_log("svc asset_work_order_reply 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $server->checkReturn(['data' =>$data]);
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc asset_work_order_reply 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc asset_work_order_reply 异常信息:" . $e->getMessage(), 'notice');
                    }
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            //FOR OA 资产工单-关闭
            ->withCallback('asset_work_order_close', function ($locale, $params) use ($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc asset_work_order_close 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new AssetWorkOrderServer($locale['locale'], $timezone);
                    $data = $server->closeForOa($params);
                    $logger->write_log("svc asset_work_order_close 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $server->checkReturn(['data' =>$data]);
                } catch (\Exception $e) {
                    if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                        $logger->write_log("svc asset_work_order_close 异常信息:" . $e->getMessage(), 'info');
                    } else {
                        $logger->write_log("svc asset_work_order_close 异常信息:" . $e->getMessage(), 'notice');
                    }
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            // by汇总审批待发短信
            ->withCallback('gatherApprovalSmsToBeSent', function ($locale, $params) use ($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc gatherApprovalSmsToBeSent 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                try {
                    $server = new AuditListServer($locale['locale'], $timezone);
                    $data = $server->gatherApprovalSmsToBeSent($params);
                    $logger->write_log("svc gatherApprovalSmsToBeSent 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $server->checkReturn(['data' =>$data]);
                } catch (\Exception $e) {
                    $logger->write_log('svc gatherApprovalSmsToBeSent 异常信息:' . $e->getFile()
                        .' line '.$e->getLine()
                        .' message '.$e->getMessage()
                        .' trace '.$e->getTraceAsString(), 'error');
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            // 合同超时处理
            ->withCallback('doContractSignAndFeedback', function ($locale, $params) use ($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc 合同超时处理 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');

                try {
                    $server = HrStaffContractServer::getInstance($locale['locale'],$timezone);
                    $data = $server->doContractSignAndFeedback($params,HrStaffContractEnums::CONTRACT_OPT_TYPE_TIME_OUT, $params['staff_info_id']);
                    $logger->write_log("svc 合同超时处理 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $server->checkReturn(['data' =>$data]);
                } catch (\Exception $e) {
                    $logger->write_log('svc 合同超时处理 异常信息:' . $e->getFile()
                        .' line '.$e->getLine()
                        .' message '.$e->getMessage()
                        .' trace '.$e->getTraceAsString(), 'error');
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }

            })
            //试用期月薪制合同工后台提交评估
            ->withCallback('storeProbationAuditByHCM', function ($locale, $params) use ($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc storeProbationAuditByHCM 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');

                try {
                    $server = new ProbationServer($locale['locale'],$timezone);
                    $data = $server->storeProbationAuditByHCM($params);
                    $logger->write_log("svc storeProbationAuditByHCM 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $data;
                } catch (\Exception $e) {
                    $logger->write_log('svc doContractSignAndFeedback 异常信息:' . $e->getFile()
                        .' line '.$e->getLine()
                        .' message '.$e->getMessage()
                        .' trace '.$e->getTraceAsString(), 'error');
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }

            })
            //个人代理申请 工具撤销功能
            ->withCallback('hire_change_cancel', function ($locale, $params) use ($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("svc hire_change_cancel 参数列表 " . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');

                try {
                    $server = new HireTypeChangeServer($locale['locale'],$timezone);
                    $params['is_tool'] = 1;
                    $params['status'] = enums::APPROVAL_STATUS_CANCEL;
                    $data = $server->updateStatus($params);
                    return $server->checkReturn(['data' =>$data]);
                } catch (ValidationException|BusinessException $e) {
                    $return['data'] = [];
                    $return['code'] = 0;
                    $return['msg']  = $e->getMessage();
                    return $return;
                } catch (\Exception $e) {
                    $logger->write_log('svc hire_change_cancel 异常信息:' . $e->getFile()
                        .' line '.$e->getLine()
                        .' message '.$e->getMessage()
                        .' trace '.$e->getTraceAsString(), 'error');
                    $return['data'] = [];
                    $return['code'] = 0;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            //菲律宾 考勤处罚
            ->withCallback('attendancePenalty', function ($locale, $params) use ($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log(['params' => $params, 'locale' => $locale, 'timezone' => $timezone], 'info');
                try {
                    $server = new AttendancePenaltyServer($locale['locale'], $timezone);
                    $data   = $server->push($params['staff_info_id'], $params['date_at'], $params['src']);
                    return $server->checkReturn(['data' => $data]);
                } catch (\Exception $e) {
                    $logger->write_log('svc attendancePenalty 异常信息:' . $e->getFile()
                        . ' line ' . $e->getLine()
                        . ' message ' . $e->getMessage()
                        . ' trace ' . $e->getTraceAsString(), 'error');
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            // 耗材提醒(消息+cc)
            ->withCallback('messageReminder', function ($locale, $params) use ($timezone) {

                $this->logger->write_log(['params' => $params, 'locale' => $locale, 'timezone' => $timezone], 'info');
                try {
                    return ReminderServer::getInstance($locale['locale'], $timezone)->sendCcAndMessage($params);
                } catch (\Exception $e) {
                    $this->logger->write_log('svc remindMaterial 异常信息:' . $e->getFile()
                        . ' line ' . $e->getLine()
                        . ' message ' . $e->getMessage()
                        . ' trace ' . $e->getTraceAsString(), 'error');
                }
            })
            //19149【ALL｜FBI】锁区网点三段码错分上报、审核、豁免
            //获取 flash box 跳转地址 问题表单提交页
            ->withCallback('get_flash_box_url', function ($locale) use($timezone) {
                $logger = $this->getDI()->get('logger');
                try {
                    $server = new CeoMailServer($locale['locale'], $timezone);
                    $server    = Tools::reBuildCountryInstance($server, [$locale['locale'], $timezone]);
                    $data = $server->getFlashBoxUrlIncreaseFromCache();
                    $logger->write_log("svc get_flash_box_url 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $server->checkReturn(['data' => $data]);
                } catch (\Exception $e) {
                    $logger->write_log("svc get_flash_box_url 异常信息:" . $e->getMessage(), 'notice');
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            //19891【PH｜BY/WHR/OA｜个人代理】合同到期续约与解约流程
            //20667【MY｜BY/WHR｜个人代理】合同到期续约与解约流程
            // 创建续约审批流
            ->withCallback('renew_contract_create_audit', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log(['params' => $params, 'locale' => $locale, 'timezone' => $timezone], 'info');
                try {
                    $server = Tools::reBuildCountryInstance(new RenewContractBusinessServer($locale['locale'], $timezone), [$locale['locale'], $timezone]);
                    $data = $server->addAudit($params);
                    $logger->write_log("svc renew_contract_create_audit 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $server->checkReturn(['data' => $data]);
                } catch (\Exception $e) {
                    $logger->write_log('svc renew_contract_create_audit 异常信息:' . $e->getFile()
                        . ' line ' . $e->getLine()
                        . ' message ' . $e->getMessage()
                        . ' trace ' . $e->getTraceAsString(), 'error');
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            // osm 重置密码，重置密码错误次数
            ->withCallback('osm_reset_login_error_lock', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log(['params' => $params, 'locale' => $locale, 'timezone' => $timezone], 'info');
                try {
                    $server = new ToolServer($locale['locale'], $timezone);
                    $cache_key = LoginServer::PASSWORD_WRONG_KEY_PREFIX . $params['company_phone'];
                    $data = $server->del_redis_key($cache_key);
                    $logger->write_log("svc osm_reset_login_error_lock 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $server->checkReturn(['data' => $data]);
                } catch (\Exception $e) {
                    $logger->write_log('svc osm_reset_login_error_lock 异常信息:' . $e->getFile()
                        . ' line ' . $e->getLine()
                        . ' message ' . $e->getMessage()
                        . ' trace ' . $e->getTraceAsString(), 'error');
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            ->withCallback('newAttendanceCalendar', function ($locale, $params) use($timezone) {
                $logger = $this->getDI()->get('logger');
                try {
                    $attendanceCalendarServer = Tools::reBuildCountryInstance(
                        new AttendanceCalendarServer($locale['locale'], $timezone),
                        [$locale['locale'], $timezone]
                    );
                    $data = $attendanceCalendarServer->init($params['staff_id'], $params['years'])->attendanceCalendar(['shift_prefix_filter' => true]);
                    $logger->write_log("svc newAttendanceCalendar 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                    return $attendanceCalendarServer->checkReturn(['data' => $data]);
                } catch (\Exception $e) {
                    $logger->write_log("svc newAttendanceCalendar 异常信息:" . $e->getMessage(), 'notice');
                    $return['data'] = [];
                    $return['code'] = -1;
                    $return['msg']  = $e->getMessage();
                    return $return;
                }
            })
            //对外提供审批相关接口
            ->withClassAndMethod('systemReject', ApprovalRpc::getInstance($timezone), 'systemReject')
            ->withClassAndMethod('SystemExternalApprovalAdd', ApprovalRpc::getInstance($timezone), 'SystemExternalApprovalAdd')
            ->withClassAndMethod('SystemExternalApprovalEdit', ApprovalRpc::getInstance($timezone), 'SystemExternalApprovalEdit')
            ->withClassAndMethod('SystemExternalApprovalUpdate', ApprovalRpc::getInstance($timezone), 'SystemExternalApprovalUpdate')
            ->withClassAndMethod('SystemExternalApprovalGetLog', ApprovalRpc::getInstance($timezone), 'SystemExternalApprovalGetLog')
            ->withClassAndMethod('SystemExternalApprovalGetList', ApprovalRpc::getInstance($timezone), 'SystemExternalApprovalGetList')
            ->withClassAndMethod('SystemExternalApprovalGetPendingCount', ApprovalRpc::getInstance($timezone), 'SystemExternalApprovalGetPendingCount')
            ->withClassAndMethod('SystemExternalApprovalGetAllList', ApprovalRpc::getInstance($timezone), 'SystemExternalApprovalGetAllList')
            ->withClassAndMethod('SystemExternalReferenceData', ApprovalRpc::getInstance($timezone), 'SystemExternalReferenceData')
            ->withClassAndMethod('SystemExternalGetStaffPendingBySerialNo', ApprovalRpc::getInstance($timezone), 'SystemExternalGetStaffPendingBySerialNo')
            ->withClassAndMethod('SystemExternalTimeOut', ApprovalRpc::getInstance($timezone), 'SystemExternalTimeOut')

            //Fleet加班车相关接口
            //获取列表
            ->withClassAndMethod('getFleetList', FleetRpc::getInstance($timezone), 'getFleetList')
            //获取列表count
            ->withClassAndMethod('lineCount', FleetRpc::getInstance($timezone), 'lineCount')
            //根据线路ID获取详情
            ->withClassAndMethod('getFleetDetailByLineId', FleetRpc::getInstance($timezone), 'getFleetDetailByLineId')
            //获取审批ID获取详情
            ->withClassAndMethod('getFleetDetailByAuditId', FleetRpc::getInstance($timezone), 'getFleetDetailByAuditId')
            //编辑加班车详情
            ->withClassAndMethod('editFleetInfo', FleetRpc::getInstance($timezone), 'editFleetInfo')
            //审批加班车
            ->withClassAndMethod('auditFleet', FleetRpc::getInstance($timezone), 'auditFleet')
            //获取带审批列表
            ->withClassAndMethod('getPendingFleet', FleetRpc::getInstance($timezone), 'getPendingFleet')
            //查询加班车需求量, 供给量和未供给量
            ->withClassAndMethod('getDemandNum', FleetRpc::getInstance($timezone), 'getDemandNum')
            //保存操作日志
            ->withClassAndMethod('saveFleetOptLog', FleetRpc::getInstance($timezone), 'saveFleetOptLog')
            //获取操作日志列表
            ->withClassAndMethod('getFleetOptLogList', FleetRpc::getInstance($timezone), 'getFleetOptLogList')
            //根据线路ID作废加班车
            ->withClassAndMethod('abandonFleetLineByLineId', FleetRpc::getInstance($timezone), 'abandonFleetLineByLineId')
            //根据审批id获取出勤信息
            ->withClassAndMethod('getVanCourierAttendanceByAuditId', FleetRpc::getInstance($timezone), 'getVanCourierAttendanceByAuditId')
            //获取图片附件
            ->withClassAndMethod('findSysAttachment', FleetRpc::getInstance($timezone), 'findSysAttachment')
            //发送调度消息
            ->withClassAndMethod('sendSchedulingChangeMessage', FleetRpc::getInstance($timezone), 'sendSchedulingChangeMessage')
            //创建加班车审批
            ->withClassAndMethod('createFleet', FleetRpc::getInstance($timezone), 'createFleet')

            //oa 批量审批加班需求
            ->withClassAndMethod('listApprovalOvertime', Overtime::getInstance($timezone), 'overtimeApprovalList')
            ->withClassAndMethod('detailApprovalOvertime', Overtime::getInstance($timezone), 'overtimeDetail')
            ->withClassAndMethod('cancelApprovalOvertime', Overtime::getInstance($timezone), 'overtimeCancel')
            //oa 批量审批加班需求(外协)
            ->withClassAndMethod('listOsOvertime', Overtime::getInstance($timezone), 'osOvertimeApprovalList')
            ->withClassAndMethod('cancelOsOvertime', Overtime::getInstance($timezone), 'osOvertimeCancel')
            ->withClassAndMethod('getOsOtType', Overtime::getInstance($timezone), 'getOsOtType')
            //java 获取可派送任务工号
            ->withClassAndMethod('onSupportStaff', SupportRpc::getInstance($timezone), 'onSupportStaff')

            //越南印尼 rpc 登陆验证
            ->withClassAndMethod('loginCheck', LoginCheck::getInstance($timezone), 'loginCheck')

            //ForJava 获取员工信息
            //转岗相关接口
            ->withClassAndMethod('get_job_transfer_hc_list', JobTransfer::getInstance($timezone), 'getHcList')
            ->withClassAndMethod('get_job_transfer_hc_list_v2', JobTransfer::getInstance($timezone), 'getHcListV2')
            ->withClassAndMethod('update_job_transfer', JobTransfer::getInstance($timezone), 'editJobTransferInfo')
            ->withClassAndMethod('audit_job_transfer', JobTransfer::getInstance($timezone), 'updateJobTransfer')
            ->withClassAndMethod('audit_job_transfer_v2', JobTransfer::getInstance($timezone), 'approvalTransfer')
            ->withClassAndMethod('bp_audit_job_transfer', JobTransfer::getInstance($timezone), 'bpApproval')
            ->withClassAndMethod('get_transfer_process', JobTransfer::getInstance($timezone), 'getJobTransferProcess')
            ->withClassAndMethod('getAfterManagerId', JobTransfer::getInstance($timezone), 'getAfterManagerId')
            //获取转岗审批流+操作流
            ->withClassAndMethod('get_apply_detail', JobTransfer::getInstance($timezone), 'getJobTransferOperateLog')
            ->withClassAndMethod('get_transfer_operate_log', JobTransfer::getInstance($timezone), 'getTransferOperateLog')
            ->withClassAndMethod('check_apply', JobTransfer::getInstance($timezone), 'checkApply')
            ->withClassAndMethod('check_batch_apply', JobTransfer::getInstance($timezone), 'checkJobTransferApplyV2')
            ->withClassAndMethod('get_batch_add_progress', JobTransfer::getInstance($timezone), 'getBatchAddProgress')
            ->withClassAndMethod('set_batch_add_number', JobTransfer::getInstance($timezone), 'setBatchAddNumber')
            ->withClassAndMethod('activate_job_transfer', JobTransfer::getInstance($timezone), 'activateTransfer')
            ->withClassAndMethod('create_special_job_transfer', JobTransfer::getInstance($timezone), 'createSpecialJobTransfer')
            ->withClassAndMethod('get_transfer_hire_type_list', JobTransfer::getInstance($timezone), 'getTransferHireTypeList')
            //请假相关
            ->withClassAndMethod('getFamilyDeathEnum', Vacation::getInstance($timezone), 'getFamilyDeathEnum')

            //转正评估  激活
            ->withClassAndMethod('probation_active', Probation::getInstance($timezone), 'active')
            //ph 合同工v3 系统后台编辑分数 给被评估人发信息
            ->withClassAndMethod('submitProbationEvaluationContractV3FromHcm', Probation::getInstance($timezone), 'submitProbationEvaluationContractV3FromHcm')
            //员工管理相关
            ->withClassAndMethod('get_staff_hrbp', StaffManage::getInstance($timezone), 'findHRBP')
            ->withClassAndMethod('get_staff_list_for_crm', StaffManage::getInstance($timezone), 'getStaffListForCRM')
            ->withClassAndMethod('getStaffManageList', StaffManage::getInstance($timezone), 'getStaffManageRegionPieceList')//批量获取员工管辖大区片区
            //出差
            ->withClassAndMethod('addTripRpc', BusinessTrip::getInstance($timezone), 'addTripRpc')
            //网点联系人
            ->withClassAndMethod('get_store_contact_person_info', Store::getInstance($timezone), 'getStoreManagerContactPersonInfo')
            ->withClassAndMethod('check_staff_is_trip', BusinessTrip::getInstance($timezone), 'checkStaffIsTrip')
            //停职申请-审批
            ->withClassAndMethod('suspension_audit', SuspensionRpc::getInstance($timezone), 'suspensionAudit')
            ->withClassAndMethod('suspension_audit_check', SuspensionRpc::getInstance($timezone), 'suspensionAuditCheck')
            //EHS-恢复在职
            ->withClassAndMethod('resume_from_suspension', SuspensionRpc::getInstance($timezone), 'reinstatement')

            ->withClassAndMethod('probation_detail_non_front_line', Probation::getInstance($timezone), 'detailNonFrontLine')
            ->withClassAndMethod('probation_score_submit_non_front_line', Probation::getInstance($timezone), 'scoreSubmitNonFrontLine')
            ->withClassAndMethod('getBusinessTripList', BusinessTrip::getInstance($timezone), 'getBusinessTripListRpc')
            ->withClassAndMethod('send_probation_formal_message', Probation::getInstance($timezone), 'sendProbationFormalMessage')
            //This is Demo
            ->withClassAndMethod('test', Demo::getInstance($timezone), 'test')
            ->withClassAndMethod('getPendingCountV1', Demo::getInstance($timezone), 'getPendingCountV1')
            ->withClassAndMethod('getPendingCountV2', Demo::getInstance($timezone), 'getPendingCountV2')
            //加班车 待审批 数据
            ->withClassAndMethod('get_fleet_pending_list', FleetRpc::getInstance($timezone), 'getFleetPendingList')
            ->withClassAndMethod('ai_bank_card', Personinfo::getInstance($timezone), 'aiBankCard')
            ->withClassAndMethod('addOsOvertime', Overtime::getInstance($timezone), 'addOsOvertime')
            ->withClassAndMethod('getRandomId', ApprovalRpc::getInstance($timezone), 'getRandomIdNo')//获取审批流编号

            ->withClassAndMethod('get_message_hot_num', MessageRpc::getInstance($timezone), 'getMessageHotNum')////KIT移动端，消息未读红点数 提供给BI(message 项目)
            ->withClassAndMethod('internal_recruit_generate_img', InternalRecruitRpc::getInstance($timezone), 'internalRecruitGenerateImg')
            ->withClassAndMethod('internal_recruit_generate_qr_code', InternalRecruitRpc::getInstance($timezone), 'internalRecruitGenerateQrCode')
            ->withObject(new RPCStub($timezone));

            //这分号单独占一行 别动
        ;

        $data['data'] = [];

        echo $server->execute();
        return false;
    }
}
