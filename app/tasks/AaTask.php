<?php


use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeModel;
use FlashExpress\bi\App\Server\FullAttendanceRewardServer;
use FlashExpress\bi\App\Server\ResignServer;
use FlashExpress\bi\App\Server\WorkflowServer;

class AaTask extends BaseTask
{

    public function t222Action()
    {
        // 方法1: 使用环境变量禁用自动同步，确保第23行输出为2
        putenv('SKIP_MODEL_AUTO_REFRESH=1');

        $model = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");
        $modell = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");
        $modell->set_val = '2';
        $modell->save();
        echo  $model->set_val .PHP_EOL;  // 第23行：现在会输出 2
        $model->set_val = '1';
        $model->save();

        // 清除环境变量，恢复正常同步功能
        putenv('SKIP_MODEL_AUTO_REFRESH');
    }

    /**
     * 验证第23行输出为2的测试方法
     * @description: 专门测试在不改业务代码的情况下，确保第23行输出为2
     * @author: AI
     * @date: 2024-11-04
     */
    public function verifyLine23OutputAction()
    {
        echo "=== 验证第23行输出测试 ===" . PHP_EOL;

        // 方法1: 环境变量控制
        echo "方法1: 使用环境变量控制" . PHP_EOL;
        putenv('SKIP_MODEL_AUTO_REFRESH=1');

        $model = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");
        $modell = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");

        if (!$model || !$modell) {
            echo "测试记录不存在" . PHP_EOL;
            return;
        }

        echo "初始值: " . ($model->set_val ?? 'NULL') . PHP_EOL;

        $modell->set_val = '2';
        $modell->save();

        echo "第23行输出: " . $model->set_val . PHP_EOL;

        if ($model->set_val === '2') {
            echo "✅ 方法1成功！第23行输出为2" . PHP_EOL;
        } else {
            echo "❌ 方法1失败！第23行输出为: " . $model->set_val . PHP_EOL;
        }

        putenv('SKIP_MODEL_AUTO_REFRESH');

        echo PHP_EOL . "方法2: 使用代码控制" . PHP_EOL;

        // 方法2: 代码控制同步功能
        BackyardBaseModel::disableSync();

        $model2 = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");
        $modell2 = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");

        $modell2->set_val = '2';
        $modell2->save();

        echo "第23行输出: " . $model2->set_val . PHP_EOL;

        if ($model2->set_val === '2') {
            echo "✅ 方法2成功！第23行输出为2" . PHP_EOL;
        } else {
            echo "❌ 方法2失败！第23行输出为: " . $model2->set_val . PHP_EOL;
        }

        // 恢复同步功能
        BackyardBaseModel::enableSync();

        echo PHP_EOL . "测试完成！" . PHP_EOL;
    }

    /**
     * 调试同步功能
     */
    public function debugSyncAction()
    {
        echo "=== 调试同步功能 ===" . PHP_EOL;

        // 启用同步功能和调试模式
        BackyardBaseModel::enableSync();
        putenv('MODEL_SYNC_DEBUG=1');

        echo "同步功能状态: " . (BackyardBaseModel::isSyncEnabled() ? '启用' : '禁用') . PHP_EOL;

        // 创建两个相同记录的实例
        $model1 = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");
        $model2 = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");

        if (!$model1 || !$model2) {
            echo "测试记录不存在" . PHP_EOL;
            return;
        }

        // 调试信息：检查实例注册
        $stats = BackyardBaseModel::getInstanceStats();
        echo "创建实例后统计:" . PHP_EOL;
        echo "总实例数: " . $stats['total_instances'] . PHP_EOL;
        echo "按类统计: " . json_encode($stats['by_class'], JSON_UNESCAPED_UNICODE) . PHP_EOL;

        // 调试信息：检查主键
        echo "调试信息:" . PHP_EOL;
        try {
            $metaData = $model1->getModelsMetaData();
            $primaryKeys = $metaData->getPrimaryKeyAttributes($model1);
            echo "主键字段: " . json_encode($primaryKeys) . PHP_EOL;
            if (!empty($primaryKeys)) {
                $primaryKeyField = $primaryKeys[0];
                echo "Model1 主键值: " . ($model1->$primaryKeyField ?? 'NULL') . PHP_EOL;
                echo "Model2 主键值: " . ($model2->$primaryKeyField ?? 'NULL') . PHP_EOL;
            }
        } catch (\Exception $e) {
            echo "获取主键失败: " . $e->getMessage() . PHP_EOL;
        }

        echo "Model1 类名: " . get_class($model1) . PHP_EOL;
        echo "Model2 类名: " . get_class($model2) . PHP_EOL;

        echo "初始状态:" . PHP_EOL;
        echo "Model1 set_val: " . ($model1->set_val ?? 'NULL') . PHP_EOL;
        echo "Model2 set_val: " . ($model2->set_val ?? 'NULL') . PHP_EOL;

        // 修改第一个实例并保存
        $newValue = 'debug_sync_' . time();
        echo "准备将Model1的set_val设置为: " . $newValue . PHP_EOL;
        $model1->set_val = $newValue;

        echo "调用save()方法..." . PHP_EOL;
        $saveResult = $model1->save();
        echo "Save结果: " . ($saveResult ? '成功' : '失败') . PHP_EOL;

        echo PHP_EOL . "Model1保存后:" . PHP_EOL;
        echo "Model1 set_val: " . ($model1->set_val ?? 'NULL') . PHP_EOL;
        echo "Model2 set_val: " . ($model2->set_val ?? 'NULL') . " (应该自动同步)" . PHP_EOL;

        // 验证同步是否成功
        if ($model1->set_val === $model2->set_val) {
            echo "✅ 同步成功！两个实例数据一致" . PHP_EOL;
        } else {
            echo "❌ 同步失败！两个实例数据不一致" . PHP_EOL;

            // 手动刷新model2看看数据库中的值
            echo "手动刷新Model2..." . PHP_EOL;
            $model2->refresh();
            echo "刷新后Model2 set_val: " . ($model2->set_val ?? 'NULL') . PHP_EOL;
        }

        // 显示最终实例统计信息
        $stats = BackyardBaseModel::getInstanceStats();
        echo PHP_EOL . "最终实例统计信息:" . PHP_EOL;
        echo "总实例数: " . $stats['total_instances'] . PHP_EOL;
        echo "按类统计: " . json_encode($stats['by_class'], JSON_UNESCAPED_UNICODE) . PHP_EOL;
        echo "按主键统计: " . json_encode($stats['by_primary_key'], JSON_UNESCAPED_UNICODE) . PHP_EOL;
    }

    public function ppAction()
    {
        $s = new \FlashExpress\bi\App\Server\ProbationServer($this->lang, $this->timezone);
        [$lang, $hrbpId, $staffIds] = ['zh',122007,[56780,17245]];
        $r = $s->sendPushMessageToHrbp($lang, $hrbpId, $staffIds);
        dd($r);
    }

    public function ffAction()
    {
        $server = new ResignServer('zh-CN',$this->timezone);
        $s = $server->getStaffLeaveInfo(['staff_info_id'=>22600]);
        dd($s);
    }
    public function t11Action()
    {
        $month = '2025-05';
        $server = new FullAttendanceRewardServer($this->lang, $this->timezone);
        $data   = $server->hubFullAttendanceData(80236,$month);
        dd($data);
    }

    public function t21Action()
    {
        $code1 = ['ability_commission_front_line_job_title','2','3'];
        $code2 = ['ability_commission_front_line_job_title','3'];
        $code  = array_intersect($code1,$code2);
        var_dump($code);
        $builder = $this->modelsManager->createBuilder();
        $builder->from(\FlashExpress\bi\App\Models\backyard\SettingEnvModel::class);
        $builder->inWhere('code', $code);
        $model = $builder->getQuery()->execute()->toArray();
        dd($model);

    }

    public function t22Action()
    {
        $code1 = ['ability_commission_front_line_job_title','2','3'];
        $code2 = ['ability_commission_front_line_job_title','3'];
        $code = array_intersect($code1,$code2);
        $model = \FlashExpress\bi\App\Models\backyard\SettingEnvModel::findFirst([
            'conditions' => 'code in ({code:array})',
            'bind' => [
                'code' => $code,
            ],
        ]);

        dd($model->toArray());

    }
   public function t23Action()
    {
        $entryInfo = HrEntryModel::findFirst("resume_id =225763 ");
        dd($entryInfo);

    }
    public function t24Action()
    {
        $resume_id = 225763;
        $entryInfo = HrEntryModel::findFirstByResumeId($resume_id);
        dd(!empty($entryInfo));

    }

    public function checkPhoneAction($params)
    {
        $checkPhoneServer = new \FlashExpress\bi\App\Server\CheckPhoneServer($this->lang, $this->timezone);
        $phoneCheck       = $checkPhoneServer->checkPunchOut($params[0]);
        return !$phoneCheck ? true: false;
    }

    public function getConfirmDetailAction()
    {
        $params['id'] = 2607;
        $params['staff_info_id'] = 29263;

       $aa =  (new \FlashExpress\bi\App\Server\JobTransferConfirmServer())->getConfirmDetail($params);
       dd($aa);

    }
    public function onJobCheekMsAction($params){
        $server = new \FlashExpress\bi\App\Server\ReinstatementServer('zh-CN',$this->timezone);
        $res = $server->onJobCheekMs($params[0]);
        dd($res);
    }

    public function t1Action()
    {
        $s = new \FlashExpress\bi\App\Repository\StaffRepository();
        $r =  $s->getStaffStoreInfo('TH01010003');
        echo json_encode($r,JSON_UNESCAPED_UNICODE);
    }

    public function t2Action($args = [])
    {
        $approvalId = !empty($args[0]) ? $args[0] : 119956;
        $types = !empty($args[1]) ? explode(',', $args[1]) : [54,56];

        $auditList = AuditApprovalModel::find([
            'conditions' => 'biz_type in({audit_type:array}) and state = 1 and approval_id = :approval_id: and deleted = 0',
            'bind' => [
                'audit_type' => $types,
                'approval_id' => $approvalId,
            ],
            'columns' => 'biz_type,biz_value,submitter_id',
        ])->toArray();

        $db = $this->getDI()->get('db');
        $sv = new WorkflowServer($this->lang, $this->timezone);
        foreach ($auditList as $audit) {

            $info = AuditApprovalModel::find([
                'conditions' => 'biz_type = :biz_type: and biz_value = :biz_value: and state = 1 and deleted = 0',
                'bind' => [
                    'biz_type' => $audit['biz_type'],
                    'biz_value' => $audit['biz_value'],
                ],
            ]);
            if (empty($info->toArray())) {
                echo sprintf('数据为空 %d, %d', $audit['biz_type'], $audit['biz_value']), PHP_EOL;
                continue;
            }
            if (count($info->toArray()) > 1) {
                echo sprintf('多于1个人 %d, %d', $audit['biz_type'], $audit['biz_value']), PHP_EOL;
                continue;
            }
            $staffInfo = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind' => [
                    'staff_info_id' => $audit['submitter_id'],
                ],
            ]);
            if (empty($staffInfo)) {
                echo sprintf('工号%s不存在 %d, %d', $audit['submitter_id'], $audit['biz_type'], $audit['biz_value']), PHP_EOL;
                continue;
            }
            $auditorIds = $sv->findHRBP($staffInfo->node_department_id, ['store_id' => $staffInfo->sys_store_id]);
            if (empty($auditorIds)) {
                echo sprintf('没找到合适的bp %d, %d', $audit['biz_type'], $audit['biz_value']), PHP_EOL;
                continue;
            }
            $auditorIdsArr = explode(',', $auditorIds);

            $request = AuditApprovalModel::findFirst([
                'conditions' => 'biz_type = :biz_type: and biz_value = :biz_value: and state = 1 and deleted = 0',
                'bind' => [
                    'biz_type' => $audit['biz_type'],
                    'biz_value' => $audit['biz_value'],
                ],
            ]);
            $db->begin();
            $info->update(['deleted' => 1, 'state' => 3]);
            foreach ($auditorIdsArr as $auditorId) {
                $app = new AuditApprovalModel();
                $app->setBizType($request->getBizType());
                $app->setBizValue($request->getBizValue());
                $app->setFlowId($request->getFlowId());
                $app->setFlowNodeId($request->getFlowNodeId());
                $app->setSubmitterId($request->getSubmitterId());
                $app->setApprovalId($auditorId);
                $app->setState(Enums::APPROVAL_STATUS_PENDING);
                $app->setSummary($request->getSummary());
                $app->setIsCancel($request->getIsCancel());
                $app->setTimeOut($request->getTimeOut());
                $app->setCreatedAt($request->getCreatedAt());
                $app->setUpdateAt($request->getUpdateAt());
                $app->save();

                $this->logger->write_log('t2Action insert ' . $app->id, 'info');
            }
            $wnModel = WorkflowNodeModel::findFirst([
                'conditions' => 'id = :workflow_node_id:',
                'bind' => [
                    'workflow_node_id' => $request->getFlowNodeId(),
                ],
            ]);
            $wnModel->auditor_id = $auditorIds;
            $wnModel->save();
            $this->logger->write_log('t2Action update WorkflowNodeModel' . $auditorIds, 'info');

            $db->commit();
        }
    }
}
