<?php

use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;

/**
 * 模型实例数据同步功能测试任务
 * @description: 测试进程内模型实例数据同步功能的各种场景
 * @author: AI
 * @date: 2024-11-04
 */
class ModelSyncTestTask extends BaseTask
{
    /**
     * 基础同步功能测试
     * @description: 测试基本的实例数据同步功能
     */
    public function basicSyncTestAction()
    {
        echo "=== 基础同步功能测试 ===" . PHP_EOL;
        
        // 启用同步功能
        BackyardBaseModel::enableSync();
        
        // 创建两个相同记录的实例
        $model1 = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");
        $model2 = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");
        
        if (!$model1 || !$model2) {
            echo "测试记录不存在，请先创建测试数据" . PHP_EOL;
            return;
        }
        
        echo "初始状态:" . PHP_EOL;
        echo "Model1 set_val: " . ($model1->set_val ?? 'NULL') . PHP_EOL;
        echo "Model2 set_val: " . ($model2->set_val ?? 'NULL') . PHP_EOL;
        
        // 修改第一个实例并保存
        $model1->set_val = 'sync_test_' . time();
        $model1->save();
        
        echo PHP_EOL . "Model1保存后:" . PHP_EOL;
        echo "Model1 set_val: " . ($model1->set_val ?? 'NULL') . PHP_EOL;
        echo "Model2 set_val: " . ($model2->set_val ?? 'NULL') . " (应该自动同步)" . PHP_EOL;
        
        // 验证同步是否成功
        if ($model1->set_val === $model2->set_val) {
            echo "✅ 同步成功！两个实例数据一致" . PHP_EOL;
        } else {
            echo "❌ 同步失败！两个实例数据不一致" . PHP_EOL;
        }
        
        // 显示实例统计信息
        $stats = BackyardBaseModel::getInstanceStats();
        echo PHP_EOL . "实例统计信息:" . PHP_EOL;
        echo "总实例数: " . $stats['total_instances'] . PHP_EOL;
        echo "按类统计: " . json_encode($stats['by_class'], JSON_UNESCAPED_UNICODE) . PHP_EOL;
    }
    
    /**
     * 禁用同步功能测试
     * @description: 测试禁用同步功能后的行为
     */
    public function disableSyncTestAction()
    {
        echo "=== 禁用同步功能测试 ===" . PHP_EOL;
        
        // 禁用同步功能
        BackyardBaseModel::disableSync();
        echo "同步功能已禁用: " . (BackyardBaseModel::isSyncEnabled() ? '否' : '是') . PHP_EOL;
        
        // 创建两个相同记录的实例
        $model1 = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");
        $model2 = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");
        
        if (!$model1 || !$model2) {
            echo "测试记录不存在，请先创建测试数据" . PHP_EOL;
            return;
        }
        
        echo "初始状态:" . PHP_EOL;
        echo "Model1 set_val: " . ($model1->set_val ?? 'NULL') . PHP_EOL;
        echo "Model2 set_val: " . ($model2->set_val ?? 'NULL') . PHP_EOL;
        
        // 修改第一个实例并保存
        $originalValue = $model2->set_val;
        $model1->set_val = 'no_sync_test_' . time();
        $model1->save();
        
        echo PHP_EOL . "Model1保存后:" . PHP_EOL;
        echo "Model1 set_val: " . ($model1->set_val ?? 'NULL') . PHP_EOL;
        echo "Model2 set_val: " . ($model2->set_val ?? 'NULL') . " (应该保持原值)" . PHP_EOL;
        
        // 验证是否没有同步
        if ($model2->set_val === $originalValue) {
            echo "✅ 禁用同步成功！Model2保持原值" . PHP_EOL;
        } else {
            echo "❌ 禁用同步失败！Model2被意外同步" . PHP_EOL;
        }
        
        // 重新启用同步
        BackyardBaseModel::enableSync();
        echo "同步功能已重新启用" . PHP_EOL;
    }
    
    /**
     * 环境变量控制测试
     * @description: 测试通过环境变量控制同步功能
     */
    public function envControlTestAction()
    {
        echo "=== 环境变量控制测试 ===" . PHP_EOL;
        
        // 设置环境变量禁用同步
        putenv('SKIP_MODEL_AUTO_REFRESH=1');
        echo "设置环境变量 SKIP_MODEL_AUTO_REFRESH=1" . PHP_EOL;
        
        // 启用同步功能（但环境变量会覆盖）
        BackyardBaseModel::enableSync();
        
        // 创建两个相同记录的实例
        $model1 = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");
        $model2 = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");
        
        if (!$model1 || !$model2) {
            echo "测试记录不存在，请先创建测试数据" . PHP_EOL;
            return;
        }
        
        $originalValue = $model2->set_val;
        $model1->set_val = 'env_test_' . time();
        $model1->save();
        
        echo "Model1保存后:" . PHP_EOL;
        echo "Model1 set_val: " . ($model1->set_val ?? 'NULL') . PHP_EOL;
        echo "Model2 set_val: " . ($model2->set_val ?? 'NULL') . " (环境变量控制，应该不同步)" . PHP_EOL;
        
        // 验证环境变量控制是否生效
        if ($model2->set_val === $originalValue) {
            echo "✅ 环境变量控制成功！同步被正确禁用" . PHP_EOL;
        } else {
            echo "❌ 环境变量控制失败！同步没有被禁用" . PHP_EOL;
        }
        
        // 清除环境变量
        putenv('SKIP_MODEL_AUTO_REFRESH');
        echo "已清除环境变量" . PHP_EOL;
    }
    
    /**
     * 性能测试
     * @description: 测试大量实例时的同步性能
     */
    public function performanceTestAction()
    {
        echo "=== 性能测试 ===" . PHP_EOL;
        
        BackyardBaseModel::enableSync();
        
        // 创建多个实例
        $instances = [];
        $instanceCount = 10;
        
        echo "创建 {$instanceCount} 个实例..." . PHP_EOL;
        for ($i = 0; $i < $instanceCount; $i++) {
            $instances[] = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");
        }
        
        // 显示统计信息
        $stats = BackyardBaseModel::getInstanceStats();
        echo "实例统计: " . json_encode($stats, JSON_UNESCAPED_UNICODE) . PHP_EOL;
        
        // 测试同步性能
        $startTime = microtime(true);
        $instances[0]->set_val = 'perf_test_' . time();
        $instances[0]->save();
        $endTime = microtime(true);
        
        $syncTime = ($endTime - $startTime) * 1000; // 转换为毫秒
        echo "同步 " . ($instanceCount - 1) . " 个实例耗时: " . number_format($syncTime, 2) . " ms" . PHP_EOL;
        
        // 验证所有实例都已同步
        $syncedCount = 0;
        $targetValue = $instances[0]->set_val;
        foreach ($instances as $instance) {
            if ($instance->set_val === $targetValue) {
                $syncedCount++;
            }
        }
        
        echo "成功同步的实例数: {$syncedCount}/{$instanceCount}" . PHP_EOL;
        
        if ($syncedCount === $instanceCount) {
            echo "✅ 性能测试通过！所有实例同步成功" . PHP_EOL;
        } else {
            echo "❌ 性能测试失败！部分实例同步失败" . PHP_EOL;
        }
        
        // 清理实例
        BackyardBaseModel::clearAllInstances();
        echo "已清理所有实例引用" . PHP_EOL;
    }
    
    /**
     * 综合测试
     * @description: 运行所有测试用例
     */
    public function allTestsAction()
    {
        echo "开始运行所有测试用例..." . PHP_EOL . PHP_EOL;
        
        $this->basicSyncTestAction();
        echo PHP_EOL . str_repeat('-', 50) . PHP_EOL . PHP_EOL;
        
        $this->disableSyncTestAction();
        echo PHP_EOL . str_repeat('-', 50) . PHP_EOL . PHP_EOL;
        
        $this->envControlTestAction();
        echo PHP_EOL . str_repeat('-', 50) . PHP_EOL . PHP_EOL;
        
        $this->performanceTestAction();
        
        echo PHP_EOL . "所有测试用例执行完成！" . PHP_EOL;
    }
}
