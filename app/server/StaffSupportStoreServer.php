<?php


namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\RestClient;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;
use FlashExpress\bi\App\Models\backyard\HrStaffApplySupportStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStaffApplySupportStoreSplitModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use Exception;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftMiddleBackupModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftMiddleDateModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftOperateLogModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Models\backyard\SupportOvertimeBackupModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Repository\AttendanceRepository;

class StaffSupportStoreServer  extends AuditBaseServer
{
    public $timezone;

    public function genSummary(int $auditId, $user)
    {
    }

    public function getWorkflowParams($auditId, $user, $state = null)
    {
    }

    public function getDetail(int $auditId, $user, $comeFrom)
    {
    }
    public function __construct($lang = 'zh-CN', $timezone = '+07:00')
    {
        parent::__construct($lang, $timezone);
    }

    /**
     * 获取支援中的数据
     * @param $staff_info_id
     * @param $date_at
     * @return array
     */
    public function getStaffSupportingInfo($staff_info_id, $date_at): array
    {
        $returnData       = [
            'staff_info_id' => intval($staff_info_id),
            'is_support'    => false,
            'store_id'      => '',
            'begin_date'    => '',
            'end_date'      => '',
        ];
        $att_re           = new AttendanceRepository($this->lang, $this->timezone);
        $supportStaffInfo = $att_re->getSupportOsStaffInfo($staff_info_id, $date_at);
        if ($supportStaffInfo) {
            $returnData['is_support'] = true;
            $returnData['store_id']   = $supportStaffInfo['store_id'];
            $returnData['begin_date'] = $supportStaffInfo['employment_begin_date'];
            $returnData['end_date']   = $supportStaffInfo['employment_end_date'];
        }
        return $returnData;
    }


    /**
     * 设置回调属性
     * @param int $auditId
     * @param int $state
     * @param null $extend
     * @param bool $isFinal
     * @return mixed|void
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $params['status'] = $state;
            if ($state == enums::APPROVAL_STATUS_APPROVAL) {
                $params['approval_agreed_time'] = gmdate('Y-m-d H:i:s');
            } else {
                $params['support_status'] = 4;
            }
            $this->getDI()->get('db')->updateAsDict(
                'hr_staff_apply_support_store',
                $params,
                'id = ' . $auditId
            );
            if ($state == enums::APPROVAL_STATUS_APPROVAL) {
                $apply_detail = $shiftParam = $this->getStaffApplySupportDetail($auditId);
                $today = date('Y-m-d');
                $isOpen = (new SettingEnvServer())->getSetVal('sub_staff_support_switch');
                $sub_staff_info_id = 0;
                //菲律宾是0 进不来
                if($isOpen == 1) {
                    if($today >= $apply_detail['employment_begin_date'] && $today <= $apply_detail['employment_end_date']) {
                        //创建子账号，回写到申请表
                        $sub_result = $this->createSubStaff($apply_detail);
                        if ($sub_result['code'] == 1) {
                            //同步ms数据
                            $sub_staff_info_id = $sub_result['data']['sub_staff_info_id'] ?? 0;
                            $this->syncMsSupportApply($apply_detail, $sub_staff_info_id);
                        }
                        if(!empty($sub_staff_info_id)){
                            //新增子账号班次
                            $shiftParam['sub_staff_info_id'] = $sub_staff_info_id;
                            $shiftParam['employment_begin_date'] = $today;
                            $this->createSubStaffShift($shiftParam);
                        }

                    }
                }

                if($today >= $apply_detail['employment_begin_date']) {
                    $shiftParam['employment_begin_date'] = $today;
                    $update_field = ['sub_staff_info_id' => $sub_staff_info_id, 'support_status' => 1];
                    if($today <= $apply_detail['employment_end_date']) {
                        $update_field = ['sub_staff_info_id' => $sub_staff_info_id, 'support_status' => 2, 'actual_begin_date' => $today];
                    }
                    if($today > $apply_detail['employment_end_date']) {
                        $update_field = ['sub_staff_info_id' => $sub_staff_info_id, 'support_status' => 3];
                    }

                    $this->getDI()->get('db')->updateAsDict(
                        'hr_staff_apply_support_store',
                        $update_field,
                        'id = ' . $apply_detail['id']
                    );
                }

                if($today <= $apply_detail['employment_end_date']) {
                    //只要支援没结束 把主账号班次替换了
                    $this->createMasterStaffShift($shiftParam);

                    //发送消息
                    $detail = $this->getStaffApplySupportDetail($auditId);
                    $staff_info_id = $detail['staff_info_id'];
                    //获取收信人语言环境
                    $lang = (new StaffServer)->getLanguage($staff_info_id);
                    //获取语言
                    $t = $this->getTranslation($lang);
                    $message_title = $t->_('staff_support_support_store');
                    $content = $t->_('store_support_staff_message_v2',
                        [
                            'support_store' => $detail['store_name'],
                            'employment_begin_date' => $detail['employment_begin_date'],
                            'employment_end_date' => $detail['employment_end_date'],
                            'shift' => $detail['work_shift']
                        ]
                    );

                    $id = time() . $staff_info_id . rand(1000000, 9999999);
                    $param['staff_users'] = array($staff_info_id);//数组 多个员工id
                    $param['message_title'] = $message_title;
                    $param['message_content'] = addslashes("<div style='font-size: 30px'>" . $content . "</div>");;
                    $param['staff_info_ids_str'] = $staff_info_id;
                    $param['id'] = $id;
                    $param['category'] = -1;
                    try {
                        $this->getDI()->get('logger')->write_log([
                            'function' => 'StaffSupportStoreServer-setProperty',
                            'params' => $param
                        ], 'info');
                        $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $lang));
                        $bi_rpc->setParams($param);
                        $res = $bi_rpc->execute();
                        $this->getDI()->get('logger')->write_log([
                            'function' => 'StaffSupportStoreServer-setProperty',
                            'message' => '消息发送成功',
                            'params' => $param,
                            'result' => $res
                        ], 'info');
                    } catch (Exception $e) {
                        $this->getDI()->get('logger')->write_log([
                            'function' => 'StaffSupportStoreServer-setProperty',
                            'message' => $e->getMessage(),
                            'line' => $e->getLine(),
                            'file' => $e->getFile()
                        ], 'error');
                    }
                }
                if(isCountry('PH')){
                    //写队列 改ot 时间
                    $mqParam['id']          = $auditId;
                    $mqParam['change_type'] = SupportOvertimeBackupModel::LOG_TYPE_APPLY;
                    $mqParam['lang']        = $this->lang;
                    $rmq                    = new RocketMQ('ot-support-shift');
                    $rmq->sendToMsg($mqParam);//有序
                }

            }
        }
    }

    /**
     * 申请详情
     * @param $apply_id
     * @return array
     */
    public function getStaffApplySupportDetail($apply_id)
    {
        $staff_apply_detail = HrStaffApplySupportStoreModel::findFirst([
            'conditions' => "id = :apply_id:",
            'bind' => [
                'apply_id' => $apply_id
            ]
        ]);
        $detail = $staff_apply_detail ? $staff_apply_detail->toArray():[];

        if($detail){
            $detail['work_shift'] = (new StoreSupportServer($this->lang,$this->timezone))->getWorkShift($detail['shift_id'],$detail['shift_extend_id']);
        }

        return $detail;
    }

    public function getSupportStaffJobTitleConfig(): array
    {

        $settingEnv = new SettingEnvServer();
        $support_staff_apply_job_title_config = $settingEnv->getSetVal('support_staff_apply_job_title_config');
        $support_job_title_role_config = $settingEnv->getSetVal('support_job_title_role_config');

        $support_staff_apply_job_title_config = json_decode($support_staff_apply_job_title_config, true); //员工职位对应可支援职位列表
        $support_job_title_role_config        = json_decode($support_job_title_role_config, true); //支援职位对应角色列表

        $support_staff_apply_job_title_ids = array_keys($support_staff_apply_job_title_config); //员工可支援职位ids
        $support_job_title_ids             = array_keys($support_job_title_role_config);        //支援职位ids


        return [
            'support_staff_apply_job_title_ids' => $support_staff_apply_job_title_ids,    //员工可去支援的职位ids
            'support_job_title_ids'             => $support_job_title_ids,                //支援的职位ids
            'support_staff_job_title_config'    => $support_staff_apply_job_title_config, //员工职位和支援职位关联
            'support_job_title_role_config'     => $support_job_title_role_config,        //支援职位和角色关系配置
        ];
    }

    /**
     * 创建子账号
     * @param array $apply_detail
     * @return array
     */
    public function createSubStaff($apply_detail = [])
    {
        try {
            if (empty($apply_detail)) {
                $this->getDI()->get('logger')->write_log(['message' => 'createSubStaff-支援信息为空'], 'error');
                return ['code' => 0, 'data' => []];
            }

            $staff_info = HrStaffInfoModel::findFirst([
                'columns' => "staff_info_id,name,sex,week_working_day,identity,mobile,personal_email,sys_department_id,node_department_id,hire_type,rest_type",
                'conditions' => "staff_info_id = :staff_info_id:",
                'bind' => [
                    'staff_info_id' => $apply_detail['staff_info_id']
                ]
            ]);
            if (empty($staff_info)) {
                $this->getDI()->get('logger')->write_log([
                    'message' => 'createSubStaff-查询不到工号数据',
                    'params' => $apply_detail
                ], 'error');
                return ['code' => 0, 'data' => []];
            }
            $staff_info = $staff_info->toArray();

            /*
            $staff_position = HrStaffInfoPositionModel::find([
                'conditions' => "staff_info_id = :staff_info_id:",
                'bind' => [
                    'staff_info_id' => $apply_detail['staff_info_id']
                ]
            ])->toArray();
            */
            //0分配员/1快递员/2仓管员/4网点出纳/18网点主管
            $position_category = [];
            switch ($apply_detail['job_title_id']) {
                case enums::$job_title['van_courier']: //110
                case enums::$job_title['bike_courier']: //13
                case enums::$job_title['tricycle_courier']: //1000
                case enums::$job_title['car_courier']: //1199
                    $position_category = [1];
                    break;
                case enums::$job_title['dc_officer']: //37
                    $position_category = [2];
                    break;
                case enums::$job_title['branch_supervisor']: //16
                    $position_category = [0, 1, 2, 4, 18];
                    break;
            }
            //没需求，跟hcm对齐
            $supportStaffJobTitleConfig = $this->getSupportStaffJobTitleConfig();
            $position_category          = $supportStaffJobTitleConfig['support_job_title_role_config'][$apply_detail['job_title_id']] ?? $position_category;


            $staff_item = HrStaffItemsModel::find([
                'conditions' => "staff_info_id = :staff_info_id:",
                'bind' => [
                    'staff_info_id' => $apply_detail['staff_info_id']
                ]
            ])->toArray();
            $manager = '';
            if (!empty($staff_item)) {
                $staff_item = array_column($staff_item, 'value', 'item');
                $manager = $staff_item['MANGER'];
            }

            $staff_arr = [
                'name'                  => $staff_info['name'],
                'sex'                   => $staff_info['sex'],
                'week_working_day'      => $staff_info['week_working_day'],
                'working_day_rest_type' => $staff_info['week_working_day'] . $staff_info['rest_type'],
                'identity'              => $staff_info['identity'],
                'mobile'                => $staff_info['mobile'],
                'hire_type'             => $staff_info['hire_type'],
                'personal_email'        => $staff_info['personal_email'],
                'job_title'             => $apply_detail['job_title_id'],   //职位id
                'sys_store_id'          => $apply_detail['store_id'],       //网点id
                'sys_department_id'     => $staff_info['sys_department_id'],//部门id
                'node_department_id'    => $staff_info['node_department_id'],
                'formal'                => 1,
                'state'                 => 1,
                'is_sub_staff'          => 1,
                'hire_date'             => date('Y-m-d', strtotime($apply_detail['employment_begin_date'])),
                'master_staff'          => $apply_detail['staff_info_id'],
                'leave_date'            => null,
                'position_category'     => $position_category,
                'manager'               => $manager,
            ];

            $hr_rpc = (new ApiClient('hr_rpc', '', 'create_sub_staff_info', 'zh-CN'));
            $hr_rpc->setParams($staff_arr);
            $res = $hr_rpc->execute();
            if (isset($res['result']['code']) && $res['result']['code'] == 1) {
                $sub_staff_info_id = $res['result']['data']['staff_info_id'] ?? 0;
                /*
                $today = date('Y-m-d');
                //$support_status = 1;
                $update_field = ['sub_staff_info_id' => $sub_staff_info_id, 'support_status' => 1];
                if($today >= $apply_detail['employment_begin_date'] && $today <= $apply_detail['employment_end_date']) {
                    //$support_status = 2;
                    $update_field = ['sub_staff_info_id' => $sub_staff_info_id, 'support_status' => 2, 'actual_begin_date' => $today];
                }
                if($today > $apply_detail['employment_end_date']) {
                    //$support_status = 3;
                    $update_field = ['sub_staff_info_id' => $sub_staff_info_id, 'support_status' => 3];
                }

                $this->getDI()->get('db')->updateAsDict(
                    'hr_staff_apply_support_store',
                    $update_field,
                    'id = ' . $apply_detail['id']
                );
                */
                $this->getDI()->get('logger')->write_log([
                    'message' => '创建子账号成功',
                    'params' => $staff_arr,
                    'result' => $res
                ], 'info');
                return ['code' => 1, 'data' => ['sub_staff_info_id' => $sub_staff_info_id]];
            } else {
                $this->getDI()->get('logger')->write_log([
                    'message' => '创建子账号失败',
                    'params' => $staff_arr,
                    'result' => $res
                ], 'error');
                return ['code' => 0, 'data' => []];
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log([
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'params' => $apply_detail
            ], 'error');
            return ['code' => 0, 'data' => []];
        }
    }

    /**
     * 同步ms 支援信息
     * @param array $apply_detail
     * @param $sub_staff_info_id
     * @return bool
     */
    public function syncMsSupportApply($apply_detail = [], $sub_staff_info_id)
    {
        if (empty($apply_detail)) {
            $this->getDI()->get('logger')->write_log(['message' => 'syncMsApply-支援信息为空'], 'error');
            return false;
        }

        if (empty($sub_staff_info_id)) {
            $this->getDI()->get('logger')->write_log(['message' => 'syncMsApply-子账号数据有误'], 'error');
            return false;
        }

        $employment_begin_date = strtotime($apply_detail['employment_begin_date']);
        $employment_end_date   = strtotime($apply_detail['employment_end_date']) + 86399; //结束日期加23:59
        $params                = [[
            'id'           => $apply_detail['id'],
            'staff_id'     => $sub_staff_info_id,
            'master_staff' => $apply_detail['staff_info_id'],
            'begin_at'     => $employment_begin_date,//支援开始时间
            'end_at'       => $employment_end_date,  //支援结束时间
        ]];
        $api                   = new RestClient('nws');
        $res                   = $api->execute(RestClient::METHOD_POST, '/svc/staff/generate/addStaffSupportInfo',
            $params, ['Accept-Language' => $this->lang]);
        if (!isset($res['code']) || $res['code'] != 1) {
            $this->getDI()->get('logger')->write_log([
                'message'      => '同步ms员工支援信息失败',
                'params'       => $params,
                'apply_detail' => $apply_detail,
                'result'       => $res,
            ]);
        }
    }

    //根据网点id获取组织架构上网点负责人姓名和手机号
    public function getStoreManagerMobile($store_id)
    {
        $store = SysStoreModel::findFirst([
            'columns' => 'id, name, manager_id, manage_region, manage_piece',
            'conditions' => 'id = :storeId:',
            'bind' => ['storeId' => $store_id]
        ]);
        $staff_name = '';
        $mobile = '';
        $mobile_company = '';
        $district_manager = [
            'district_manager_name' => '',
            'district_manager_mobile' => '',
        ];
        $store = !empty($store) ? $store->toArray() : [];
        if (!empty($store)) {
            $staff_info = $staff_info = $this->getStaffMobile($store['manager_id']);
            $mobile = $staff_info['mobile'] ?? '';
            $mobile_company = $staff_info['mobile_company'] ?? '';
            $staff_name = $staff_info['name'] ?? '';

            $district_manager = $this->getDistrictManagerMobile($store['manage_piece']);
        }

        return [
            'district_manager_name' => $district_manager['district_manager_name'] ?? '',
            'district_manager_mobile' => $district_manager['district_manager_mobile'] ?? '',
            'branch_supervisor_name' => $staff_name,
            'branch_supervisor_mobile' => !empty($mobile_company) ? $mobile_company : $mobile,
        ];
    }

    //获取片区负责人姓名手机号
    public function getDistrictManagerMobile($piece_id)
    {
        if(empty($piece_id)) {
            return [
                'district_manager_name' => '',
                'district_manager_mobile' => '',
            ];
        }
        $piece_info = SysManagePieceModel::findFirst([
            'columns' => 'id, name, manage_region_id, manager_id',
            'conditions' => 'id = :piece_id:',
            'bind' => ['piece_id' => $piece_id]
        ]);
        $staff_name = '';
        $mobile = '';
        $mobile_company = '';
        $piece_info = $piece_info ? $piece_info->toArray() : [];
        if (!empty($piece_info)) {
            $staff_info = $this->getStaffMobile($piece_info['manager_id']);
            $mobile = $staff_info['mobile'] ?? '';
            $mobile_company = $staff_info['mobile_company'] ?? '';
            $staff_name = $staff_info['name'] ?? '';
        }

        return [
            'district_manager_name' => $staff_name,
            'district_manager_mobile' => !empty($mobile_company) ? $mobile_company : $mobile,
        ];
    }

    //根据工号获取手机号
    public function getStaffMobile($staff_info_id)
    {
        if (empty($staff_info_id)) {
            return [];
        }
        $staff_info = HrStaffInfoModel::findFirst([
            'columns' => 'staff_info_id, name, mobile, mobile_company, state',
            'conditions' => 'staff_info_id = :staff_info_id: and state = 1',
            'bind' => ['staff_info_id' => $staff_info_id]
        ]);
        $staff_info = !empty($staff_info) ? $staff_info->toArray() : [];

        return $staff_info;
    }

    //判断是否与其他工单时间冲突
    public function validateConflict($employment_begin_date, $employment_end_date, $staff_info_id) {
        $detail = HrStaffApplySupportStoreModel::find([
            'conditions' => "staff_info_id = :staff_info_id: and status in (1,2) and employment_end_date >= :employment_begin_date: and employment_begin_date <= :employment_end_date: and support_status != 4",
            'bind' => [
                'employment_begin_date' => $employment_begin_date,
                'employment_end_date' => $employment_end_date,
                'staff_info_id' => $staff_info_id
            ],
        ])->toArray();
        if(!empty($detail)) {
            return true;
        }
        return false;
    }

    //获取员工基本信息
    public function getStaffInfo($staff_info_id) {
        $staff = HrStaffInfoModel::findFirst([
            'columns' => "staff_info_id,name,sex,identity,mobile,personal_email,job_title,sys_store_id,sys_department_id,node_department_id",
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind' => [
                'staff_info_id'  => $staff_info_id,
            ]
        ]);
        return !empty($staff) ? $staff->toArray() : [];
    }


    /**
     * 支援审核通过 并且已经过了支援开始时间 增加子账号班次
     * @param $supportDetail
     * @return bool
     */
    public function createSubStaffShift($supportDetail)
    {
        $sub_staff_info_id = $supportDetail['sub_staff_info_id'];
        $shift_start_date  = $supportDetail['employment_begin_date'];
        $shift_end_date    = $supportDetail['employment_end_date'];
        $days = DateHelper::DateRange(strtotime($shift_start_date), strtotime($shift_end_date));
        //账号班次新增
        $subStaffShit = [];
        foreach ($days as $day) {
            $subStaffShit[]  = [
                'staff_info_id' => $sub_staff_info_id,
                'shift_date'    => $day,
                'shift_id'      => $supportDetail['shift_id'],
                'shift_type'    => $supportDetail['shift_type'],
                'shift_start'   => $supportDetail['shift_start'],
                'shift_end'     => $supportDetail['shift_end'],
                'operator_id'   => 10000,
            ];
        }
        (new HrStaffShiftMiddleDateModel())->batch_insert($subStaffShit, BackyardBaseModel::WRITE_DB_PHALCON_DI_NAME);
        return true;
    }

    /**
     * 支援审核通过 替换主账号班次为支援班次
     * @param $supportDetail
     */
    public function createMasterStaffShift($supportDetail){
        $staff_info_id     = $supportDetail['staff_info_id'];
        $shift_start_date  = $supportDetail['employment_begin_date'];
        $shift_end_date    = $supportDetail['employment_end_date'];

        //备份主账号班次信息 删除原数据
        $staffShift = HrStaffShiftMiddleDateModel::find([
            'conditions' => ' staff_info_id = :staff_info_id: and shift_date >= :start_date: and shift_date <= :end_date:',
            'bind'       => [
                'staff_info_id' => $staff_info_id,
                'start_date'    => $shift_start_date,
                'end_date'      => $shift_end_date,
            ],
        ]);
        if(!empty($staffShift->toArray())){
            $staffShiftBackup = [];
            foreach ($staffShift as $shift) {
                $staffShiftBackup[] = [
                    'staff_info_id' => $shift->staff_info_id,
                    'shift_date'    => $shift->shift_date,
                    'shift_id'      => $shift->shift_id,
                    'shift_type'    => $shift->shift_type,
                    'shift_start'   => $shift->shift_start,
                    'shift_end'     => $shift->shift_end,
                    'operator_id'   => $shift->operator_id,
                    'remark'        => 'by add',
                ];
            }
            (new HrStaffShiftMiddleBackupModel())->batch_insert($staffShiftBackup, BackyardBaseModel::WRITE_DB_PHALCON_DI_NAME);
            $staffShift->update(['deleted' => 1]);
        }

        $days = DateHelper::DateRange(strtotime($shift_start_date), strtotime($shift_end_date));
        //主账号 班次新增
        $staffNewShift = [];
        foreach ($days as $day) {
            $staffNewShift[] = [
                'staff_info_id' => $staff_info_id,
                'shift_date'    => $day,
                'shift_id'      => $supportDetail['shift_id'],
                'shift_type'    => $supportDetail['shift_type'],
                'shift_start'   => $supportDetail['shift_start'],
                'shift_end'     => $supportDetail['shift_end'],
                'operator_id'   => 10000,
            ];
        }
        (new HrStaffShiftMiddleDateModel())->batch_insert($staffNewShift, BackyardBaseModel::WRITE_DB_PHALCON_DI_NAME);
        //加班次变更操作日志
        $logParam['staff_info_id'] = $staff_info_id;
        $logParam['date_at']       = $shift_start_date;
        $logParam['operate_id']    = $staff_info_id;
        $logType                   = HrStaffShiftOperateLogModel::EDIT_TYPE_PRESET_SHIFT;
        $extend['before']          = '';
        $extend['after']           = "{$supportDetail['shift_start']}-{$supportDetail['shift_end']} ({$shift_start_date}~{$shift_end_date})";
        $logServer                 = new WorkdayServer();
        $logServer->addShiftLog($logType, $logParam, $extend);
        return true;
    }

    //根据区间获取支援信息
    public function getSupportDataBetween($staffId, $start, $end, $subStaffId = 0){
        $conditions = 'status = 2 and support_status in (1,2,3) ';
        $bind = [];

        if(!empty($subStaffId)){
            $conditions .= ' and sub_staff_info_id = :staff_id:';
            $bind['staff_id'] = $subStaffId;
        }else{
            $conditions .= ' and staff_info_id = :staff_id:';
            $bind['staff_id'] = $staffId;
        }
        $conditions .= ' and !(employment_end_date < :start_date: or employment_begin_date > :end_date:)';
        $bind['start_date'] = $start;
        $bind['end_date'] = $end;

        $data = HrStaffApplySupportStoreModel::find([
            'columns' => 'id, employment_begin_date, employment_end_date, store_id, store_name',
            'conditions' => $conditions,
            'bind' => $bind
        ])->toArray();
        return $data;
    }


    //支援日期拆分表 写数据 包括子账号和主账号
    public function addSupportSplit($supportInfo)
    {
        $startDate = $supportInfo['employment_begin_date'];
        $endDate   = $supportInfo['employment_end_date'];
        if (empty($startDate) || empty($endDate)) {
            return;
        }
        $insert   = [];
        $dateList = DateHelper::DateRange(strtotime($startDate), strtotime($endDate));
        foreach ($dateList as $date) {
            $row['staff_info_id']   = $supportInfo['staff_info_id'];
            $row['origin_id']       = $supportInfo['id'];
            $row['employment_date'] = $date;
            $row['is_separate']     = $supportInfo['is_separate'];
            $insert[]               = $row;
        }
        $model = new HrStaffApplySupportStoreSplitModel();
        return $model->batch_insert($insert);
    }

    //获取 可分配任务的工号 https://flashexpress.feishu.cn/docx/KCWjdduttoBKHvxJ6fVcRL3BnEd
    public function getMissionStaff($param)
    {
        if (empty($param['staffInfoIds'])) {
            return [];
        }
        $staffIds = $param['staffInfoIds'];
        $date     = $param['dateAt'];
        $storeId  = $param['storeId'];

        //排除 支援 并且 存在 子账号的主工号
        $supportIgnore = HrStaffApplySupportStoreSplitModel::find([
            'columns'    => 'staff_info_id',
            'conditions' => 'employment_date = :date_at: and staff_info_id in ({ids:array}) and is_separate = 0',
            'bind'       => ['date_at' => $date, 'ids' => $staffIds],
        ])->toArray();
        $supportIgnore = empty($supportIgnore) ? [] : array_column($supportIgnore, 'staff_info_id');
        $staffIds      = array_values(array_diff($staffIds, $supportIgnore));
        $this->logger->write_log(['getMissionStaff_support' => $supportIgnore], 'info');
        if (empty($staffIds)) {
            return [];
        }

        //只取符合考勤条件的员工
        $attendanceIgnore = StaffWorkAttendanceModel::find([
            'columns'    => 'staff_info_id',
            'conditions' => 'attendance_date = :date_at: and staff_info_id in ({ids:array}) and started_at is not null and end_at is null',
            'bind'       => ['date_at' => $date, 'ids' => $staffIds],
        ])->toArray();
        $attendanceIgnore = empty($attendanceIgnore) ? [] : array_column($attendanceIgnore, 'staff_info_id');
        $this->logger->write_log(['getMissionStaff_attendance' => $attendanceIgnore], 'info');
        return $attendanceIgnore;
    }


}