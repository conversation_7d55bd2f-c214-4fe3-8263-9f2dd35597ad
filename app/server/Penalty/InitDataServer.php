<?php


namespace FlashExpress\bi\App\Server\Penalty;

use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\HrPenaltyDetailModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Models\backyard\HrStaffTransferModel;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\AttendanceServer;
use FlashExpress\bi\App\Server\AuditServer;
use FlashExpress\bi\App\Server\BaseServer;
use FlashExpress\bi\App\Server\HrShiftServer;
use FlashExpress\bi\App\Server\StaffServer;


class InitDataServer extends BaseServer
{

    public $staff_info_id;
    public $attendance_date;

    public $job_title;
    public $sys_department_id;
    public $staff_store_id;
    public $staff_state;
    public $formal                = null;
    public $staff_lang            = 'en';
    public $need_shift_start      = null;
    public $need_shift_end        = null;
    public $shift_start           = null;
    public $shift_end             = null;
    public $attendance_started_at = null;
    public $attendance_end_at     = null;
    public $is_off_or_ph          = false;
    public $leave_type            = null;
    public $penalty_detail        = null;
    public $expiry_reason         = null;
    public $coming_late_penalty   = null;
    public $leave_early_penalty   = null;
    public $absent_penalty        = null;
    public $is_cross_days_shift   = false;//是否是跨天班次
    public $hire_type             = null;//雇佣类型
    public $related_id = null;
    public $created_at = null;

    public function __construct($lang, $timezone)
    {
        parent::__construct($lang, $timezone);
    }

    private function srcToExpiryReason($src): int
    {
        $map = [3 => 3, 4 => 4, 5 => 3];
        return $map[$src] ?? 0;
    }


    public function setParams($params): InitDataServer
    {
        $this->related_id   = $params['related_id']??null;
        $this->created_at   = $params['created_at']??null;
        $this->staff_info_id   = $params['staff_info_id'];
        $this->attendance_date = $params['attendance_date'];
        $this->expiry_reason   = $this->srcToExpiryReason($params['src'] ?? 0);
        return $this;
    }

    /**
     * 考勤数据
     * @return InitDataServer
     */
    public function build(): InitDataServer
    {

        //员工固化信息
        $this->dealStaffInfo();
        //处罚信息
        $this->getPenaltyInfo();
        //打卡 班次信息
        $this->getAttendanceInfo();
        //请假信息
        $this->getLeaveInfo();
        //构造需要打卡的时间信息
        $this->dealNeedShiftInfo();
        //员工语言
        $this->getStaffLang();
        //休息日 公共假期
        $this->dealOffPhInfo();
        return $this;
    }

    /**
     * 员工的语言包
     * @return void
     */
    protected function getStaffLang()
    {
        $this->staff_lang = (new StaffServer())->getLanguage($this->staff_info_id);
    }


    /**
     * 获取生效中的处罚数据
     * @return void
     */
    protected function getPenaltyInfo(): void
    {
        $this->penalty_detail = HrPenaltyDetailModel::find([
            'conditions' => "staff_info_id = :staff_info_id: and attendance_date = :attendance_date: and state = :state: ",
            'bind'       => [
                'staff_info_id'   => $this->staff_info_id,
                'attendance_date' => $this->attendance_date,
                'state'           => HrPenaltyDetailModel::PENALTY_STATE_TAKE_EFFECT,
            ],
        ]);

        foreach ($this->penalty_detail as $value) {
            if ($value->penalty_reason == HrPenaltyDetailModel::PENALTY_REASON_COMING_LATE) {
                $this->coming_late_penalty = $value;
            }
            if ($value->penalty_reason == HrPenaltyDetailModel::PENALTY_REASON_LEAVE_EARLY) {
                $this->leave_early_penalty = $value;
            }
            if ($value->penalty_reason == HrPenaltyDetailModel::PENALTY_REASON_AB) {
                $this->absent_penalty = $value;
            }
        }
    }

    /**
     * 是否是休息日或公共假期
     * @return void
     */
    protected function dealOffPhInfo(): void
    {
        //公共假期  休息日
        $staff_model   = new StaffRepository($this->lang);
        $bi_staff_info = $staff_model->getStaffPosition($this->staff_info_id);
        //优先使用固化表的字段
        $this->formal            = !is_null($this->formal) ? $this->formal : $bi_staff_info['formal'];
        $this->staff_store_id    = $this->staff_store_id ?: $bi_staff_info['sys_store_id'];
        $this->staff_state       = $this->staff_state ?: $bi_staff_info['state'];
        $this->job_title         = $this->job_title ?: $bi_staff_info['job_title'];
        $this->sys_department_id = $this->sys_department_id ?: $bi_staff_info['sys_department_id'];
        $this->hire_type         = $this->hire_type ?: $bi_staff_info['hire_type'];
        $working_day             = $staff_model->get_is_working_day($bi_staff_info, $this->attendance_date);
        $this->is_off_or_ph      = $working_day != StaffWorkAttendanceModel::WORK_DAY_UN_PH_UN_REST;
        echo '是否是off_or_ph : ' . intval($this->is_off_or_ph) . PHP_EOL;
    }

    /**
     * 员工信息
     */
    protected function dealStaffInfo()
    {
        $staffInfo = HrStaffTransferModel::findFirst(
            [
                'columns'    => 'staff_info_id,store_id,state,job_title,sys_department_id,formal,hire_type',
                'conditions' => ' staff_info_id = :staff_info_id: and stat_date = :stat_date: ',
                'bind'       => [
                    'staff_info_id' => $this->staff_info_id,
                    'stat_date'     => $this->attendance_date,
                ],
            ]
        );
        if (!empty($staffInfo)) {
            $this->formal            = $staffInfo->formal;
            $this->staff_store_id    = $staffInfo->store_id;
            $this->staff_state       = $staffInfo->state;
            $this->job_title         = $staffInfo->job_title;
            $this->sys_department_id = $staffInfo->sys_department_id;
            $this->hire_type         = $staffInfo->hire_type;
        }
    }


    /**
     * 请假信息
     * @return void
     */
    protected function getLeaveInfo(): void
    {
        $auditRepository = new AuditRepository($this->lang);
        $leaveInfo       = $auditRepository->getStaffLeaveInfoByDate($this->staff_info_id,
            $this->attendance_date);
        if ($leaveInfo) {
            $this->leave_type = count($leaveInfo) > 1 ? 0 : current($leaveInfo)['type'];
        }
        echo '请假信息 : '.$this->leave_type.PHP_EOL;
    }

    /**
     * 打卡 班次信息
     * @return void
     */
    protected function getAttendanceInfo(): void
    {
        //打卡  班次
        $attendance_info = (new AttendanceServer($this->lang, $this->timeZone))->get_att_by_date($this->staff_info_id,
            $this->attendance_date);
        if ($attendance_info) {
            $this->attendance_started_at = $attendance_info['started_at'];
            $this->attendance_end_at     = $attendance_info['end_at'];
            $this->shift_start           = $attendance_info['shift_start'];
            $this->shift_end             = $attendance_info['shift_end'];
            echo '工号' .$this->staff_info_id;
            echo '上班卡时间:'.date('Y-m-d H:i:s', strtotime($this->attendance_started_at)).PHP_EOL;
            echo '下班卡时间:'.date('Y-m-d H:i:s', strtotime($this->attendance_end_at)).PHP_EOL;
        } else {
            $shiftServer = new HrShiftServer();
            $shift_info = $shiftServer->getShiftInfos($this->staff_info_id,[$this->attendance_date]);
            $this->shift_start = $shift_info[$this->attendance_date]['start'] ?? '';
            $this->shift_end   = $shift_info[$this->attendance_date]['end'] ?? '';
        }
    }


    /**
     * 构建应该上班的时间
     * @return void
     */
    protected function dealNeedShiftInfo(): void
    {
        if (empty($this->shift_start) || empty($this->shift_end)) {
            return;
        }
        //需要打上班卡的时间
        $this->need_shift_start = date('Y-m-d H:i:s', strtotime($this->attendance_date.' '.$this->shift_start));
        //可以下班的时间
        $this->need_shift_end = date('Y-m-d H:i:s', strtotime($this->attendance_date.' '.$this->shift_end));
        //跨天班次
        if (strtotime($this->attendance_date.' '.$this->shift_start) > strtotime($this->attendance_date.' '.$this->shift_end)) {
            $this->is_cross_days_shift = true;
            $this->need_shift_end      = date('Y-m-d H:i:s',
                strtotime($this->attendance_date.' '.$this->shift_end." +1 day"));
        }
        //上午请假，班次开始时间 向后挪 5小时
        if ($this->leave_type == 1) {
            $this->need_shift_start = date('Y-m-d H:i:s', strtotime($this->need_shift_start) + (5 * 3600));
        }
        //请了下午假，下班时间提前5小时
        if ($this->leave_type == 2) {
            $this->need_shift_end = date('Y-m-d H:i:s', strtotime($this->need_shift_end) - (5 * 3600));
        }

        echo '上班班次时间:'.date('Y-m-d H:i:s', strtotime($this->need_shift_start)).PHP_EOL;
        echo '下班班次时间:'.date('Y-m-d H:i:s', strtotime($this->need_shift_end)).PHP_EOL;
    }


}