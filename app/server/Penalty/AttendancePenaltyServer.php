<?php


namespace FlashExpress\bi\App\Server\Penalty;

use Exception;
use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;
use FlashExpress\bi\App\Models\backyard\HrPenaltyDetailModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Server\BaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;


class AttendancePenaltyServer extends BaseServer
{

    public function __construct($lang, $timezone)
    {
        parent::__construct($lang, $timezone);
    }

    /**
     * 验证是否需要处罚
     * @param $job_title
     * @param $sys_department_id
     * @return bool
     */
    public function checkIsNeedPenalty($job_title, $sys_department_id,$sys_store_id = ''): bool
    {
        $settEnvServer = new SettingEnvServer();
        $list          = $settEnvServer->listByCodeFromCache([
            'dept_network_management_id',
            'attendance_penalty_job_title',
        ]);
        if (empty($list)) {
            return false;
        }
        if ($sys_store_id == enums::HEAD_OFFICE_ID) {
            return false;
        }

        $list = array_column($list, 'set_val', 'code');
        return in_array($job_title, explode(',',
                $list['attendance_penalty_job_title'])) && $sys_department_id == $list['dept_network_management_id'];
    }

    const START_DATE = '2023-05-01';


    /**
     * 考勤异常触发入口
     * @throws BusinessException
     * @throws Exception
     */
    public function fire($staff_info_id, $attendance_date, $src): bool
    {
        if ($attendance_date < self::START_DATE && RUNTIME == 'pro') {
            return true;
        }

        if ($attendance_date > date('Y-m-d')) {
            return true;
        }

        if (!isCountry('PH')) {
            return true;
        }
        switch ($src) {
            case BasePenaltyServer::SRC_PUNCH_IN://上班
                $server = new PunchInPenaltyServer($this->lang, $this->timeZone);
                break;
            case  BasePenaltyServer::SRC_PUNCH_OUT://下班
                $server = new PunchOutPenaltyServer($this->lang, $this->timeZone);
                break;
            case  BasePenaltyServer::SRC_ABSENT://缺卡
                $server = new AbsentPenaltyServer($this->lang, $this->timeZone);
                break;
            case  BasePenaltyServer::SRC_MAKE_UP://补卡
            case  BasePenaltyServer::SRC_ADD_LEAVE://请假
                $server = new FixAttendancePenaltyServer($this->lang, $this->timeZone);
                break;
            default:
                throw new BusinessException('undefined src');
        }
        $db = BackyardBaseModel::beginTransaction($this);

        try {
            $setData  = ['staff_info_id' => $staff_info_id, 'attendance_date' => $attendance_date, 'src' => $src];
            $initData = (new InitDataServer($this->lang, $this->timeZone))->setParams($setData)->build();

            if ($initData->formal != HrStaffInfoModel::FORMAL_1 || $initData->hire_type == HrStaffInfoModel::HIRE_TYPE_UN_PAID || !$this->checkIsNeedPenalty($initData->job_title,
                    $initData->sys_department_id,$initData->staff_store_id)) {
                $this->getDI()->get("logger")->write_log(['info' => '无效的部门和职位', 'data' => $setData,'initData'=>[$initData->job_title,
                    $initData->sys_department_id,$initData->staff_store_id]], "info");
                throw new BusinessException('无效的部门和职位');
            }
            $server->initData($initData)->handle();
            $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
        return true;
    }


    public function hotfix0308($staff_info_id,$created_at = '')
    {
        $db  = $this->getDI()->get('db_coupon_r');
        $sql = "select  mc.staff_info_id, m.message, m.related_id,mc.created_at
                    from message_courier mc 
                    inner join  message_content as m on mc.message_content_id = m.id
                    where  mc.is_del = 0 and  mc.`category` = 33 and mc.`category_code` = 8           
                    ";
        if(!empty($staff_info_id)){
            $sql .= " and mc.staff_info_id in (".implode(',',$staff_info_id).")";
        }
        if(empty($created_at)){
            $sql .= " and mc.created_at >= '2024-02-15 16:00:00'  ";
        }else{
            $sql .= " and mc.created_at >= '$created_at 16:00:00'  ";
        }

        $data = $db->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        foreach ($data as $datum) {
            $staff_info_id = $datum['staff_info_id'];
            $related_id    = $datum['related_id'];
            $created_at    = $datum['created_at'];

            $penaltyModel = HrPenaltyDetailModel::findFirstById($related_id);
            if (!empty($penaltyModel)) {
                continue;
            }
            $content = $datum['message'];
            preg_match('/([0-9]{4}-[0-9]{2}-[0-9]{2})/', $content, $match);
            if (empty($match)) {
                $this->getDI()->get("logger")->write_log(['info' => '日期格式有误', 'data' => $datum], "notice");
                continue;
            }
            $attendance_date = $match[0];
            $this->aaa($staff_info_id, $attendance_date, $related_id, $created_at);
        }
    }


    public function aaa($staff_info_id,$attendance_date,$related_id,$created_at){

        $server = new FixAttendancePenaltyServer($this->lang, $this->timeZone);

        $db = BackyardBaseModel::beginTransaction($this);

        try {
            $setData  = ['staff_info_id' => $staff_info_id, 'attendance_date' => $attendance_date, 'src' => BasePenaltyServer::SRC_MAKE_UP,'related_id'=>$related_id,'created_at'=>$created_at];
            $initData = (new InitDataServer($this->lang, $this->timeZone))->setParams($setData)->build();

            if ($initData->formal != HrStaffInfoModel::FORMAL_1 || $initData->hire_type == HrStaffInfoModel::HIRE_TYPE_UN_PAID || !$this->checkIsNeedPenalty($initData->job_title,
                    $initData->sys_department_id,$initData->staff_store_id)) {
                $this->getDI()->get("logger")->write_log(['info' => '无效的部门和职位', 'data' => $setData], "info");
                throw new BusinessException('1');
            }
            $server->initData($initData)->handle();
            $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
    }


    /**
     * 入队
     * @param $src
     * @param $staff_info_id
     * @param $attendance_date
     * @return mixed
     * @throws ValidationException
     */
    public function push($staff_info_id, $attendance_date, $src)
    {
        if ($attendance_date < self::START_DATE && RUNTIME == 'pro') {
            return true;
        }

        if (!isCountry('PH')) {
            return true;
        }

        if (empty($staff_info_id)) {
            throw new ValidationException('need staff_info_id');
        }
        if (empty($attendance_date)) {
            throw new ValidationException('need attendance_date');
        }

        if (empty($src)) {
            throw new ValidationException('need src');
        }

        if (in_array($src, [BasePenaltyServer::SRC_MAKE_UP, BasePenaltyServer::SRC_ADD_LEAVE])) {
            return $this->pushToQueue($staff_info_id, $attendance_date, $src,2);
        }
        //临时调整
        if ($src == 'hcm') {
            $src = BasePenaltyServer::SRC_ADD_LEAVE;
        }

        return $this->pushToQueue($staff_info_id, $attendance_date, $src);
    }

    //入队列
    public function pushToQueue($staff_info_id, $attendance_date, $src,$timeInMillis = 0)
    {
        $rmq = new RocketMQ('attendance-penalty');
        $rmq->setType(RocketMQ::TAG_ATTENDANCE_PENALTY);
        $rmq->setShardingKey($staff_info_id);
        $content = [
            'staff_info_id'   => $staff_info_id,
            'attendance_date' => $attendance_date,
            'src'             => $src,
        ];
        return $rmq->sendOrderlyMsg($content,$timeInMillis);
    }

    /**
     * @deprecated
     * @return bool
     */
    public function delayPop(): bool
    {
        if (!isCountry('PH')) {
            return true;
        }
        $redis    = $this->getDI()->get('redisLib');
        $data     = $redis->rpop(RedisEnums::LIST_ATTENDANCE_PENALTY_DAILY);
        if(empty($data)){
            return true;
        }

        sleep(2);

        $data = json_decode($data);
        try {
            $this->getDI()->get("logger")->write_log(['delayPop' => $data], "info");
            echo ' staff_info_id ' . $data->staff_info_id . ' attendance_date ' . $data->attendance_date . ' src ' . $data->src . PHP_EOL;
            $this->fire($data->staff_info_id, $data->attendance_date, $data->src);
        } catch (BusinessException $e) {
            echo $e->getMessage();
            $this->getDI()->get("logger")->write_log([
                'exception'        => $e->getMessage(),
                'getTraceAsString' => $e->getTraceAsString(),
            ], "info");
        } catch (Exception $e) {
            echo $e->getMessage() . $e->getTraceAsString();
            $this->getDI()->get("logger")->write_log([
                'exception'        => $e->getMessage(),
                'getTraceAsString' => $e->getTraceAsString(),
            ]);
        }
        return true;
    }


    /**
     * @deprecated
     * 出队
     * @throws BusinessException
     */
    public function pop(): bool
    {
        if (!isCountry('PH')) {
            return true;
        }
        $redis = $this->getDI()->get('redisLib');
        while ($data = $redis->rpop(RedisEnums::LIST_ATTENDANCE_PENALTY)) {
            $data = json_decode($data);
            try {
                $this->getDI()->get("logger")->write_log(['pop' => $data], "info");
                echo ' staff_info_id ' . $data->staff_info_id . ' attendance_date ' . $data->attendance_date . ' src ' . $data->src . PHP_EOL;
                $this->fire($data->staff_info_id, $data->attendance_date, $data->src);
            } catch (BusinessException $e) {
                echo $e->getMessage();
                $this->getDI()->get("logger")->write_log([
                    'exception'        => $e->getMessage(),
                    'getTraceAsString' => $e->getTraceAsString(),
                ], "info");
            } catch (Exception $e) {
                echo $e->getMessage() . $e->getTraceAsString();
                $this->getDI()->get("logger")->write_log([
                    'exception'        => $e->getMessage(),
                    'getTraceAsString' => $e->getTraceAsString(),
                ]);
            }
        }
        return true;
    }

    /**
     * 向FBI 生产 补卡消息
     * @param $params
     * @return bool
     */
    public function sendCardReplacementMsg($params)
    {
        if(!isCountry('TH') && !isCountry('PH')) {
            return true;
        }
        $params['current_time'] = gmdate('Y-m-d H:i:s');
        if(!empty($params['started_at']) && !empty($params['end_at'])) {
            $params['attendance_type'] = StaffWorkAttendanceModel::ATTENDANCE_ON_OFF;

        } elseif (!empty($params['started_at'])) {
            $params['attendance_type'] = StaffWorkAttendanceModel::ATTENDANCE_ON;

        } elseif (!empty($params['end_at'])) {
            $params['attendance_type'] = StaffWorkAttendanceModel::ATTENDANCE_OFF;

        } else {
            $this->logger->write_log('backyard to FBI rmq_topic_by_card_replacement error params: ' . json_encode($params, JSON_UNESCAPED_UNICODE), 'notice');
            return false;
        }

        $this->logger->write_log('backyard to FBI rmq_topic_by_card_replacement params: ' . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');

        $rmq = new RocketMQ('card_replacement_producer');
        $rmq->setType(RocketMQ::TAG_NAME_PUNCH_IN_AGAIN);
        $rid = $rmq->sendMsgByTag($params, 3);//延迟3秒
        if($rid) {
            $this->logger->write_log('backyard to FBI rmq_topic_by_card_replacement exception: ' . $rid, 'info');
            return true;
        }
        $this->logger->write_log('backyard to FBI rmq_topic_by_card_replacement send error params: ' . json_encode($params, JSON_UNESCAPED_UNICODE), 'notice');

        return false;
    }

    public function lenAction()
    {
        $redis = $this->getDI()->get('redisLib');
        echo $redis->lLen(RedisEnums::LIST_ATTENDANCE_PENALTY_DAILY);
    }


}