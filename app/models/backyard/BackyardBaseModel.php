<?php

namespace FlashExpress\bi\App\Models\backyard;

use FlashExpress\bi\App\Models\BaseModel;
class BackyardBaseModel extends BaseModel
{
    protected static $instances = [];
    protected $table_name = null;
    const WRITE_DB_PHALCON_DI_NAME = 'db';
    const READ_DB_PHALCON_DI_NAME  = 'db_rby';

    // 实例同步控制标志
    protected static $syncEnabled = true;

    public function initialize()
    {
        parent::initialize();
        $this->useDynamicUpdate(true);
        $this->registerInstance();
    }

    /**
     *
     */
    public function onConstruct()
    {
        $this->setReadConnectionService(self::READ_DB_PHALCON_DI_NAME);
        $this->setWriteConnectionService(self::WRITE_DB_PHALCON_DI_NAME);
        if ($this->getWriteConnection()->isUnderTransaction()) {
            $this->setReadConnectionService(self::WRITE_DB_PHALCON_DI_NAME);
        }
        $this->relations();
        $this->registerInstance();
    }

    protected function relations()
    {

    }

    /**
     * 注册实例到静态数组中
     * @description: 将当前模型实例注册到全局实例管理器中，用于实现进程内数据同步
     * @author: AI
     * @date: 2024-11-04
     */
    protected function registerInstance()
    {
        $class = get_class($this);
        $primaryKey = $this->getPrimaryKey();

        // 使用对象哈希作为键，确保每个实例都有唯一标识
        $key = $class . '_' . spl_object_hash($this);
        self::$instances[$key] = [
            'instance' => $this,
            'primary_key' => $primaryKey,
            'class' => $class,
            'created_at' => microtime(true)
        ];
    }

    /**
     * 获取主键值
     * @description: 获取当前模型实例的主键值，用于实例分组管理
     * @author: AI
     * @date: 2024-11-04
     * @return mixed 主键值或null
     */
    protected function getPrimaryKey()
    {
        try {
            // 获取模型的主键字段名
            $metaData = $this->getModelsMetaData();
            $primaryKeys = $metaData->getPrimaryKeyAttributes($this);

            if (!empty($primaryKeys)) {
                $primaryKeyField = $primaryKeys[0]; // 假设只有一个主键
                return $this->$primaryKeyField ?? null;
            }
        } catch (\Exception $e) {
            // 如果出现异常，返回null
            return null;
        }

        return null;
    }

    /**
     * 析构函数，清理实例引用
     * @description: 对象销毁时自动清理实例引用，防止内存泄漏
     * @author: AI
     * @date: 2024-11-04
     */
    public function __destruct()
    {
        $this->unregisterInstance();
    }

    /**
     * 从静态数组中移除实例
     * @description: 从全局实例管理器中移除当前实例
     * @author: AI
     * @date: 2024-11-04
     */
    protected function unregisterInstance()
    {
        $class = get_class($this);
        $key = $class . '_' . spl_object_hash($this);

        if (isset(self::$instances[$key])) {
            unset(self::$instances[$key]);
        }
    }

    /**
     * 重写save方法，保存后自动刷新所有已存在的实例
     * @description: 实现进程内模型实例数据同步的核心方法
     * @author: AI
     * @date: 2024-11-04
     * @param array|null $data
     * @param array|null $whiteList
     * @return bool
     */
    public function save($data = null, $whiteList = null)
    {
        // 调用父类的save方法
        $result = parent::save($data, $whiteList);

        // 如果保存成功，刷新所有相关实例
        if ($result && self::$syncEnabled) {
            $this->refreshAllInstances();
        }

        return $result;
    }

    /**
     * 刷新所有相关的实例
     * @description: 进程内模型实例数据同步的核心实现，确保相同记录的所有实例数据一致
     * @author: AI
     * @date: 2024-11-04
     */
    protected function refreshAllInstances()
    {
        $class = get_class($this);
        $primaryKey = $this->getPrimaryKey();

        // 检查是否需要跳过刷新（用于特殊测试场景）
        // 可以通过环境变量 SKIP_MODEL_AUTO_REFRESH=1 来控制
        if (getenv('SKIP_MODEL_AUTO_REFRESH') === '1') {
            return;
        }

        // 只有当存在主键时才刷新其他实例
        if ($primaryKey) {
            $refreshCount = 0;
            $errorCount = 0;

            // 遍历所有实例并刷新它们
            foreach (self::$instances as $instanceKey => $instanceData) {
                // 检查是否为相同类和相同主键，但不是当前实例
                if (isset($instanceData['primary_key']) &&
                    $instanceData['primary_key'] === $primaryKey &&
                    isset($instanceData['class']) &&
                    $instanceData['class'] === $class &&
                    isset($instanceData['instance']) &&
                    $instanceData['instance'] !== $this) {
                    try {
                        $instanceData['instance']->refresh();
                        $refreshCount++;
                    } catch (\Exception $e) {
                        // 如果刷新失败，从实例列表中移除
                        unset(self::$instances[$instanceKey]);
                        $errorCount++;
                    }
                }
            }

            // 记录同步日志（可选）
            if ($refreshCount > 0 || $errorCount > 0) {
                $this->logInstanceSync($class, $primaryKey, $refreshCount, $errorCount);
            }
        }
    }

    /**
     * 记录实例同步日志
     * @description: 记录模型实例同步的详细信息，便于调试和监控
     * @author: AI
     * @date: 2024-11-04
     * @param string $class 模型类名
     * @param mixed $primaryKey 主键值
     * @param int $refreshCount 成功刷新的实例数量
     * @param int $errorCount 刷新失败的实例数量
     */
    protected function logInstanceSync($class, $primaryKey, $refreshCount, $errorCount)
    {
        // 只在调试模式下记录日志
        if (getenv('MODEL_SYNC_DEBUG') === '1') {
            $message = sprintf(
                '[ModelSync] Class: %s, PK: %s, Refreshed: %d, Errors: %d',
                $class,
                $primaryKey,
                $refreshCount,
                $errorCount
            );

            // 如果有日志服务，记录到日志
            try {
                if ($this->getDI()->has('logger')) {
                    $this->getDI()->get('logger')->info($message);
                }
            } catch (\Exception $e) {
                // 忽略日志记录错误
            }
        }
    }

    /**
     * 启用实例同步功能
     * @description: 全局启用模型实例数据同步功能
     * @author: AI
     * @date: 2024-11-04
     */
    public static function enableSync()
    {
        self::$syncEnabled = true;
    }

    /**
     * 禁用实例同步功能
     * @description: 全局禁用模型实例数据同步功能，用于测试或特殊场景
     * @author: AI
     * @date: 2024-11-04
     */
    public static function disableSync()
    {
        self::$syncEnabled = false;
    }

    /**
     * 检查同步功能是否启用
     * @description: 检查当前模型实例同步功能的启用状态
     * @author: AI
     * @date: 2024-11-04
     * @return bool
     */
    public static function isSyncEnabled()
    {
        return self::$syncEnabled;
    }

    /**
     * 获取当前注册的实例统计信息
     * @description: 获取当前进程中注册的模型实例统计信息，用于监控和调试
     * @author: AI
     * @date: 2024-11-04
     * @return array
     */
    public static function getInstanceStats()
    {
        $stats = [
            'total_instances' => count(self::$instances),
            'by_class' => [],
            'by_primary_key' => []
        ];

        foreach (self::$instances as $instanceData) {
            $class = $instanceData['class'];
            $primaryKey = $instanceData['primary_key'];

            // 按类统计
            if (!isset($stats['by_class'][$class])) {
                $stats['by_class'][$class] = 0;
            }
            $stats['by_class'][$class]++;

            // 按主键统计
            if ($primaryKey !== null) {
                $key = $class . ':' . $primaryKey;
                if (!isset($stats['by_primary_key'][$key])) {
                    $stats['by_primary_key'][$key] = 0;
                }
                $stats['by_primary_key'][$key]++;
            }
        }

        return $stats;
    }

    /**
     * 清理所有实例引用
     * @description: 清理所有注册的实例引用，释放内存，通常在进程结束时调用
     * @author: AI
     * @date: 2024-11-04
     */
    public static function clearAllInstances()
    {
        self::$instances = [];
    }

    /**
     * 确保模型数据是最新的
     * @description: 手动刷新当前实例数据，确保数据是最新的
     * @author: AI
     * @date: 2024-11-04
     * @return bool
     */
    public function ensureFreshData()
    {
        // 直接调用父类的refresh方法来确保数据是最新的
        return parent::refresh();
    }

    public function refresh()
    {
        if ($this->getWriteConnection()->isUnderTransaction()) {
            $this->setReadConnectionService(self::WRITE_DB_PHALCON_DI_NAME);
        }
        return parent::refresh();
    }

    // 指定表名称
    public function getSource()
    {
        $className       = substr(static::class, strrpos(static::class, '\\') + 1);
        $classNamePrefix  = str_replace('Model', '', $className);
        $tableName       = strtolower(preg_replace('/(?<=[a-z])([A-Z])/', '_$1', $classNamePrefix));
        return $this->table_name ?? $tableName;
    }

    /**
     *父子数据组合
     * Created by: Lqz.
     * @param array $parent
     * @param array $children
     * @param string $parentFK
     * @param string $childrenFK
     * @param string $returnK
     * @param string $relationShip
     * @return array
     * CreateTime: 2020/8/14 0014 19:14
     */

    public static function combineParentAndChildren($parent = [], $children = [], $parentFK = 'id', $childrenFK = 'parent_id', $returnK = 'children', $relationShip = 'hasOne')
    {
        if (!in_array($relationShip, ['hasOne', 'hasMany'])) {
            throw  new \LogicException('The relationShip error');
        }
        if ($parent && $children) {
            foreach ($parent as $_k => $_p) {
                if (!isset($_p[$parentFK])) {
                    throw  new \LogicException('The parentFK error');
                }
                foreach ($children as $_c) {
                    if (!isset($_c[$childrenFK])) {
                        throw  new \LogicException('The childrenFK error');
                    }
                    if ($_p[$parentFK] == $_c[$childrenFK]) {
                        if ($relationShip == 'hasOne') {
                            $parent[$_k][$returnK] = $_c;
                        } else {
                            $parent[$_k][$returnK][] = $_c;
                        }
                    }
                }
            }
        }
        return $parent;
    }

    /**
     * 事务
     * Created by: Lqz.
     * @param $app
     * @return mixed
     * CreateTime: 2020/8/14 0014 19:15
     */
    public static function beginTransaction($app)
    {
        $db = $app->getDI()->get(self::WRITE_DB_PHALCON_DI_NAME);
        try {
            $db->begin();
        } catch (\Exception $e) {
            $app->getDI()->get("logger")->write_log("reconnect mysql...", "info");
            $db->connect();
            $db->begin();

        }
        return $db;
    }

    public function batch_insert(array $data , $db = self::WRITE_DB_PHALCON_DI_NAME){
        if (count($data) == 0) {
            throw new \LogicException('参数错误');
        }
        $keys = array_keys(reset($data));
        $keys = array_map(function ($key) {
            return "`{$key}`";
        }, $keys);
        $keys = implode(',', $keys);
        $sql = "INSERT INTO " . $this->getSource() . " ({$keys}) VALUES ";
        foreach ($data as $v) {
            $v = array_map(function ($value) {
                if ($value === null) {
                    return 'NULL';
                } else {
                    $value = addslashes($value); //处理特殊符号，如单引号
                    return "'{$value}'";
                }
            }, $v);
            $values = implode(',', array_values($v));
            $sql .= " ({$values}), ";
        }
        $sql = rtrim(trim($sql), ',');
        //DI中注册的数据库服务名称为"db"
        $result = $this->getDI()->get($db)->execute($sql);
        if (!$result) {
            throw new \LogicException('批量入库记录');
        }
        return $result;
    }

    /**
     * @description:获取插入 sql
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/4/14 17:46
     */
    public function batch_insert_to_sql($tbl_name, array $info)
    {
        if (!is_array($info) || count($info) == 0) {
            return false;
        }
        $keys = array_keys(reset($info));
        $keys = array_map(function ($key) {
            return "`{$key}`";
        }, $keys);
        $keys = implode(',', $keys);
        $sql = "INSERT INTO ".$tbl_name." ({$keys}) VALUES ";

        foreach ($info as $v) {
            $v = array_map(function ($value) {
                if ($value === null) {
                    return 'NULL';
                }

                $value = addslashes($value); //处理特殊符号，如单引号
                return "'{$value}'";
            }, $v);
            $values = implode(',', array_values($v));
            $sql .= " ({$values}), ";
        }
        $sql = rtrim(trim($sql), ',');
        return $sql;
    }


    /**
     * 获取归档历史表名 并判断是否存在
     *
     * @param $stat_date  时间
     * @param $table_name  表名
     * @param $table_name_date  追加到表名的时间格式
     * return "{$table_name}_{$table_date}";
     */
    public function is_get_table_name($stat_date, $table_name, $table_name_date = "Y")
    {
        $table_date = date($table_name_date, strtotime($stat_date));
        $new_table  = "{$table_name}_{$table_date}";
        $db         = $this->getDI()->get('db');
        $logger     = $this->getDI()->get('logger');

        try {
            $insert  = "show tables like '{$new_table}'";
            $info = $db->query($insert)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            if(empty($info)){
                return false; //代表表不存在
            }
        } catch (\Exception $e) {
            $logger->write_log("查询 by库数据归档，日期：" . $stat_date . "，{$new_table} 失败:" . $e->getMessage());
            return false; //代表表不存在
        }
        return $new_table;
    }

    /**
     * 创建归档历史表名
     *
     * @param $stat_date  时间
     * @param $table_name  表名
     * @param $table_name_date  追加到表名的时间格式
     * return "{$table_name}_{$table_date}";
     */
    public function create_table_name($stat_date, $table_name, $table_name_date = "Y")
    {
        $table_date = date($table_name_date, strtotime($stat_date));
        $new_table  = "{$table_name}_{$table_date}";
        $db         = $this->getDI()->get('db');
        $logger     = $this->getDI()->get('logger');

        try {
            $insert  = "show tables like '{$new_table}'";
            $info = $db->query($insert)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            if (empty($info)) {
                $create_table = "create table {$new_table} like {$table_name};";
                $db->execute($create_table);
                echo "by库数据归档，日期：" . $stat_date . "，{$new_table} 建表成功".PHP_EOL;
                $logger->write_log("by库数据归档，日期：" . $stat_date . "，{$new_table} 建表成功", 'info');
            }else{
                echo "by库数据归档，日期：" . $stat_date . "，{$new_table} 表已存在".PHP_EOL;
                $logger->write_log("by库数据归档，日期：" . $stat_date . "，{$new_table} 表已存在", 'info');
            }
        } catch (\Exception $e) {
            $logger->write_log("by库数据归档，日期：" . $stat_date . "，{$new_table} 建表失败:" . $e->getMessage());
            return '0';
        }
        return $new_table;
    }
}