<?php

/**
 * 业务仓库基类
 */

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\library\CacheService;

class BaseRepository extends CacheService
{
    public $timeZoneOfThailand;
    public $timezone;
    public $languagePack;
    public $t;
    public $lang;

    public function getTranslation()
    {
        $args = func_get_args();
        if (!empty($args) and $args[0] == 'push') {
            $this->lang = 'th';
        }

        return $this->languagePack->getTranslation($this->lang);
    }

    public function __construct($lang = '')
    {
        parent::__construct($lang);
        //获取全局语言对象
        $this->t                  = $this->languagePack->t;
        $this->lang               = $this->languagePack->lang;
        $this->timezone           = $this->getDI()['config']['application']['timeZone'];
        $this->timeZoneOfThailand = $this->getDI()['config']['application']['timeZoneOfThailand'];
    }


    /**
     * 获取插入语句
     * @param string $tbl_name 表名
     * @param array $info 数据
     * @return bool|string
     */
    public function getInsertDbSql($tbl_name, $info)
    {
        if (is_array($info) && !empty($info)) {
            $i = 0;
            foreach ($info as $key => $val) {
                $fields[$i] = "`".$key."`";
                $values[$i] = addcslashes(stripslashes($val), "'");
                $i++;
            }
            $s_fields = "(".implode(",", $fields).")";
            $s_values = "('".implode("','", $values)."')";
            $sql      = "INSERT INTO  
                       $tbl_name  
                       $s_fields  
                   VALUES  
                       $s_values";
            return $sql;
        } else {
            return false;
        }
    }


    /**
     * [batch_insert 批量插入]
     * @return [type] [array]
     * <AUTHOR> <[email address]>
     */
    public function batch_insert($tbl_name, array $info)
    {
        if (!is_array($info) || count($info) == 0) {
            return false;
        }
        $keys = array_keys(reset($info));
        $keys = array_map(function ($key) {
            return "`{$key}`";
        }, $keys);
        $keys = implode(',', $keys);
        $sql  = "INSERT INTO ".$tbl_name." ({$keys}) VALUES ";

        foreach ($info as $v) {
            $v      = array_map(function ($value) {
                if ($value === null) {
                    return 'NULL';
                } else {
                    $value = addslashes($value); //处理特殊符号，如单引号
                    return "'{$value}'";
                }
            }, $v);
            $values = implode(',', array_values($v));
            $sql    .= " ({$values}), ";
        }
        $sql    = rtrim(trim($sql), ',');
        $result = $this->getDI()->get('db')->execute($sql);
        return $result;
    }

    //格式化update 语句
    public function formatUpdate($data)
    {
        if (!is_array($data) || empty($data)) {
            return false;
        }
        $sql = "";
        foreach ($data as $k => $v) {
            if (is_null($v)) {
                $sql .= "`$k` = null,";
            } else {
                $sql .= "`$k` = '{$v}',";
            }
        }
        return rtrim($sql, ',');
    }


    //根据id 更新字段
    public function updateInfoByTable($table, $key_name, $key_value, $data, $db = 'db')
    {
        if (empty($data)) {
            return false;
        }

        return $this->getDI()->get($db)->updateAsDict($table, $data, ["conditions" => $key_name.'='.$key_value]);
    }

    /**
     * 从Wms获得数据
     * @param $url
     * @param $param
     * @return bool|mixed|string
     */
    public function getDataFromWms($url, $paramArr)
    {
        $params             = [];
        $params['mchId']    = env('wms_mchId');
        $params['nonceStr'] = time();
        $params             = array_merge($paramArr, $params);
        $resPost            = httpPostFun($url, $params, null, env('wms_pwd'));
        $this->getDI()->get('logger')->write_log('get_data_from_wms '.json_encode($resPost, JSON_UNESCAPED_UNICODE),
            'info');
        return $resPost;
    }

    public function class_factory($class_name)
    {
        $timeZone     = $this->config->application->timeZone;
        $country_code = $this->config->application->country_code;
        $country_code = ucfirst(strtolower($country_code));
        $class        = "\FlashExpress\bi\App\Modules\\{$country_code}\Server\\{$class_name}";
        if (class_exists($class)) {
            return new $class($this->lang, $timeZone);
        }

        $class = "\FlashExpress\bi\App\Server\\{$class_name}";
        return new $class($this->lang, $timeZone);
    }
}
